/**
 * Admin routes for Vectorize synchronization management
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { Context } from 'hono';
import { ProductSyncService } from '../../services/product-sync';
import { VectorizeAISearchService } from '../../services/vectorize-ai-search';

type Bindings = {
  DB: D1Database;
  AI: Ai;
  VECTORIZE_INDEX: VectorizeIndex;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

function handleError(c: Context, error: any, message = 'Internal server error') {
  console.error(error);
  return c.json({ success: false, error: message, details: error.message }, 500);
}

// POST /admin/vectorize/sync/full - Full product sync to Vectorize
app.post('/sync/full', async (c) => {
  try {
    console.log('Starting full Vectorize sync...');
    
    const syncService = new ProductSyncService(c.env);
    const result = await syncService.fullSync();
    
    return c.json({
      success: true,
      message: `Full sync completed: ${result.indexed} products indexed`,
      indexed: result.indexed,
      errors: result.errors
    });

  } catch (error) {
    return handleError(c, error, 'Failed to perform full Vectorize sync');
  }
});

// POST /admin/vectorize/sync/product/:id - Sync single product to Vectorize
app.post('/sync/product/:id', async (c) => {
  try {
    const productId = c.req.param('id');
    console.log(`Syncing product ${productId} to Vectorize...`);
    
    const syncService = new ProductSyncService(c.env);
    await syncService.syncProduct(productId);
    
    return c.json({
      success: true,
      message: `Product ${productId} synced successfully`
    });

  } catch (error) {
    return handleError(c, error, 'Failed to sync product to Vectorize');
  }
});

// DELETE /admin/vectorize/sync/product/:id - Remove product from Vectorize
app.delete('/sync/product/:id', async (c) => {
  try {
    const productId = c.req.param('id');
    console.log(`Removing product ${productId} from Vectorize...`);
    
    const syncService = new ProductSyncService(c.env);
    await syncService.removeProduct(productId);
    
    return c.json({
      success: true,
      message: `Product ${productId} removed from Vectorize successfully`
    });

  } catch (error) {
    return handleError(c, error, 'Failed to remove product from Vectorize');
  }
});

// POST /admin/vectorize/sync/products - Bulk sync products to Vectorize
app.post('/sync/products', async (c) => {
  try {
    const { productIds } = await c.req.json();
    
    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return c.json({ error: 'Product IDs are required' }, 400);
    }
    
    console.log(`Syncing ${productIds.length} products to Vectorize...`);
    
    const syncService = new ProductSyncService(c.env);
    await syncService.syncProducts(productIds);
    
    return c.json({
      success: true,
      message: `${productIds.length} products synced successfully`
    });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk sync products to Vectorize');
  }
});

// GET /admin/vectorize/stats - Get Vectorize index statistics
app.get('/stats', async (c) => {
  try {
    const vectorizeService = new VectorizeAISearchService(c.env);
    const stats = await vectorizeService.getIndexStats();
    
    return c.json({
      success: true,
      stats: stats
    });

  } catch (error) {
    return handleError(c, error, 'Failed to get Vectorize stats');
  }
});

export const adminVectorizeRoutes = app; 