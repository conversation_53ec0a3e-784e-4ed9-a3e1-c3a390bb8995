import { Hono } from 'hono';
import type { Env } from '../index';
import { authMiddleware } from '../middleware/auth';

const dashboardRoutes = new Hono<{ Bindings: Env; Variables: { user: any } }>();

// Apply auth middleware to all routes
dashboardRoutes.use('*', authMiddleware);

// Get comprehensive dashboard statistics
dashboardRoutes.get('/stats', async (c) => {
  try {
    const user = c.get('user');
    const companyId = user.company_id;
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    const lastMonth = currentMonth === 1 ? 12 : currentMonth - 1;
    const lastMonthYear = currentMonth === 1 ? currentYear - 1 : currentYear;

    // Current month stats
    const currentMonthStats = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as invoice_count,
        COALESCE(SUM(total), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN paid = 1 THEN total ELSE 0 END), 0) as paid_revenue,
        COALESCE(SUM(CASE WHEN paid = 0 THEN total ELSE 0 END), 0) as unpaid_revenue
      FROM invoices
      WHERE company_id = ?
        AND strftime('%m', issue_date) = ?
        AND strftime('%Y', issue_date) = ?
    `).bind(companyId, currentMonth.toString().padStart(2, '0'), currentYear.toString()).first<{ invoice_count: number; total_revenue: number; paid_revenue: number; unpaid_revenue: number }>();

    // Last month stats for comparison
    const lastMonthStats = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as invoice_count,
        COALESCE(SUM(total), 0) as total_revenue
      FROM invoices
      WHERE company_id = ?
        AND strftime('%m', issue_date) = ?
        AND strftime('%Y', issue_date) = ?
    `).bind(companyId, lastMonth.toString().padStart(2, '0'), lastMonthYear.toString()).first<{ invoice_count: number; total_revenue: number }>();

    // Overdue invoices
    const overdueInvoices = await c.env.DB.prepare(`
      SELECT COUNT(*) as count, COALESCE(SUM(total), 0) as amount
      FROM invoices
      WHERE company_id = ?
        AND paid = 0
        AND due_date < date('now')
    `).bind(companyId).first<{ count: number; amount: number }>();

    // Recent invoices (last 5)
    const recentInvoices = await c.env.DB.prepare(`
      SELECT
        i.id,
        s.series,
        i.number,
        i.issue_date,
        i.total,
        i.paid,
        i.currency,
        c.name as client_name
      FROM invoices i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN invoice_series s ON i.series_id = s.id
      WHERE i.company_id = ?
      ORDER BY i.created_at DESC
      LIMIT 5
    `).bind(companyId).all();

    // Top clients by revenue (current year)
    const topClients = await c.env.DB.prepare(`
      SELECT
        c.name,
        COUNT(i.id) as invoice_count,
        COALESCE(SUM(i.total), 0) as total_revenue
      FROM clients c
      LEFT JOIN invoices i ON c.id = i.client_id
        AND strftime('%Y', i.issue_date) = ?
      WHERE c.company_id = ?
      GROUP BY c.id, c.name
      ORDER BY total_revenue DESC
      LIMIT 5
    `).bind(currentYear.toString(), companyId).all();

    // Monthly revenue trend (last 6 months)
    const monthlyTrend = await c.env.DB.prepare(`
      SELECT
        strftime('%Y-%m', issue_date) as month,
        COALESCE(SUM(total), 0) as revenue,
        COUNT(*) as invoice_count
      FROM invoices
      WHERE company_id = ?
        AND issue_date >= date('now', '-6 months')
      GROUP BY strftime('%Y-%m', issue_date)
      ORDER BY month
    `).bind(companyId).all();

    // VAT summary for current month
    const vatSummary = await c.env.DB.prepare(`
      SELECT
        ii.vat_rate,
        COALESCE(SUM(ii.subtotal), 0) as base_amount,
        COALESCE(SUM(ii.vat_amount), 0) as vat_amount
      FROM invoice_items ii
      JOIN invoices i ON ii.invoice_id = i.id
      WHERE i.company_id = ?
        AND strftime('%m', i.issue_date) = ?
        AND strftime('%Y', i.issue_date) = ?
      GROUP BY ii.vat_rate
      ORDER BY ii.vat_rate DESC
    `).bind(companyId, currentMonth.toString().padStart(2, '0'), currentYear.toString()).all();

    // Client count
    const clientStats = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as total_clients,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_clients
      FROM clients
      WHERE company_id = ?
    `).bind(companyId).first<{ total_clients: number; active_clients: number }>();

    // Product count
    const productStats = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as total_products,
        COUNT(CASE WHEN active = 1 THEN 1 END) as active_products
      FROM products
      WHERE company_id = ?
    `).bind(companyId).first<{ total_products: number; active_products: number }>();

    // Calculate growth percentages
    const revenueGrowth = (lastMonthStats?.total_revenue ?? 0) > 0
      ? (((currentMonthStats?.total_revenue ?? 0) - (lastMonthStats?.total_revenue ?? 0)) / (lastMonthStats?.total_revenue ?? 1) * 100)
      : 0;

    const invoiceGrowth = (lastMonthStats?.invoice_count ?? 0) > 0
      ? (((currentMonthStats?.invoice_count ?? 0) - (lastMonthStats?.invoice_count ?? 0)) / (lastMonthStats?.invoice_count ?? 1) * 100)
      : 0;

    return c.json({
      success: true,
      data: {
        // Current month overview
        overview: {
          invoices_this_month: currentMonthStats?.invoice_count || 0,
          revenue_this_month: currentMonthStats?.total_revenue || 0,
          paid_revenue: currentMonthStats?.paid_revenue || 0,
          unpaid_revenue: currentMonthStats?.unpaid_revenue || 0,
          revenue_growth: Math.round(revenueGrowth * 100) / 100,
          invoice_growth: Math.round(invoiceGrowth * 100) / 100
        },

        // Overdue information
        overdue: {
          count: overdueInvoices?.count || 0,
          amount: overdueInvoices?.amount || 0
        },

        // Entity counts
        counts: {
          total_clients: clientStats?.total_clients || 0,
          active_clients: clientStats?.active_clients || 0,
          total_products: productStats?.total_products || 0,
          active_products: productStats?.active_products || 0
        },

        // Recent activity
        recent_invoices: recentInvoices?.results || [],

        // Analytics
        top_clients: topClients?.results || [],
        monthly_trend: monthlyTrend?.results || [],
        vat_summary: vatSummary?.results || []
      }
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return c.json({ success: false, message: 'Eroare la încărcarea statisticilor: ' + error.message }, 500);
  }
});

// Get quick actions data
dashboardRoutes.get('/quick-actions', async (c) => {
  try {
    const user = c.get('user');
    const companyId = user.company_id;

    // Get active invoice series
    const activeSeries = await c.env.DB.prepare(`
      SELECT id, series, type, current_number, year
      FROM invoice_series
      WHERE company_id = ? AND active = 1
      ORDER BY type, series
    `).bind(companyId).all();

    // Get recent clients (last 10 used)
    const recentClients = await c.env.DB.prepare(`
      SELECT DISTINCT c.id, c.name, c.type, c.cui
      FROM clients c
      LEFT JOIN invoices i ON c.id = i.client_id
      WHERE c.company_id = ? AND c.is_active = 1
      ORDER BY i.created_at DESC
      LIMIT 10
    `).bind(companyId).all();

    // Get top products (most used)
    const topProducts = await c.env.DB.prepare(`
      SELECT p.id, p.name, p.price, p.vat_rate, COUNT(ii.id) as usage_count
      FROM products p
      LEFT JOIN invoice_items ii ON p.id = ii.product_id
      WHERE p.company_id = ? AND p.active = 1
      GROUP BY p.id
      ORDER BY usage_count DESC, p.name
      LIMIT 10
    `).bind(companyId).all();

    return c.json({
      success: true,
      data: {
        series: activeSeries?.results || [],
        recent_clients: recentClients?.results || [],
        top_products: topProducts?.results || []
      }
    });
  } catch (error) {
    console.error('Quick actions error:', error);
    return c.json({ success: false, message: 'Eroare la încărcarea acțiunilor rapide' }, 500);
  }
});

// Get payment status overview
dashboardRoutes.get('/payments', async (c) => {
  try {
    const user = c.get('user');
    const companyId = user.company_id;

    // Payment status breakdown
    const paymentStatus = await c.env.DB.prepare(`
      SELECT
        status,
        COUNT(*) as count,
        COALESCE(SUM(total), 0) as amount
      FROM invoices
      WHERE company_id = ?
      GROUP BY status
    `).bind(companyId).all();

    // Upcoming due dates (next 30 days)
    const upcomingDue = await c.env.DB.prepare(`
      SELECT
        i.id, s.series, i.number, i.due_date, i.total, i.currency,
        c.name as client_name
      FROM invoices i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN invoice_series s ON i.series_id = s.id
      WHERE i.company_id = ?
        AND i.paid = 0
        AND i.due_date BETWEEN date('now') AND date('now', '+30 days')
      ORDER BY i.due_date ASC
      LIMIT 10
    `).bind(companyId).all();

    // Payment method breakdown
    const paymentMethods = await c.env.DB.prepare(`
      SELECT
        payment_method,
        COUNT(*) as count,
        COALESCE(SUM(total), 0) as amount
      FROM invoices
      WHERE company_id = ? AND paid = 1 AND payment_method IS NOT NULL
      GROUP BY payment_method
    `).bind(companyId).all();

    return c.json({
      success: true,
      data: {
        payment_status: paymentStatus?.results || [],
        upcoming_due: upcomingDue?.results || [],
        payment_methods: paymentMethods?.results || []
      }
    });
  } catch (error) {
    console.error('Payment overview error:', error);
    return c.json({ success: false, message: 'Eroare la încărcarea informațiilor de plată' }, 500);
  }
});

// Get business insights
dashboardRoutes.get('/insights', async (c) => {
  try {
    const user = c.get('user');
    const companyId = user.company_id;
    const currentYear = new Date().getFullYear();

    // Best performing products
    const topProducts = await c.env.DB.prepare(`
      SELECT
        p.name,
        SUM(ii.quantity) as total_quantity,
        COALESCE(SUM(ii.total), 0) as total_revenue,
        COUNT(DISTINCT ii.invoice_id) as invoice_count
      FROM products p
      JOIN invoice_items ii ON p.id = ii.product_id
      JOIN invoices i ON ii.invoice_id = i.id
      WHERE p.company_id = ?
        AND strftime('%Y', i.issue_date) = ?
      GROUP BY p.id, p.name
      ORDER BY total_revenue DESC
      LIMIT 5
    `).bind(companyId, currentYear.toString()).all();

    // Client analysis
    const clientAnalysis = await c.env.DB.prepare(`
      SELECT
        AVG(total) as avg_invoice_value,
        COUNT(DISTINCT client_id) as unique_clients,
        COUNT(*) as total_invoices,
        COALESCE(SUM(total), 0) as total_revenue
      FROM invoices
      WHERE company_id = ?
        AND strftime('%Y', issue_date) = ?
    `).bind(companyId, currentYear.toString()).first();

    // Seasonal trends (quarterly)
    const quarterlyTrends = await c.env.DB.prepare(`
      SELECT
        CASE
          WHEN CAST(strftime('%m', issue_date) AS INTEGER) BETWEEN 1 AND 3 THEN 'Q1'
          WHEN CAST(strftime('%m', issue_date) AS INTEGER) BETWEEN 4 AND 6 THEN 'Q2'
          WHEN CAST(strftime('%m', issue_date) AS INTEGER) BETWEEN 7 AND 9 THEN 'Q3'
          ELSE 'Q4'
        END as quarter,
        COUNT(*) as invoice_count,
        COALESCE(SUM(total), 0) as revenue
      FROM invoices
      WHERE company_id = ?
        AND strftime('%Y', issue_date) = ?
      GROUP BY quarter
      ORDER BY quarter
    `).bind(companyId, currentYear.toString()).all();

    return c.json({
      success: true,
      data: {
        top_products: topProducts?.results || [],
        client_analysis: clientAnalysis || {},
        quarterly_trends: quarterlyTrends?.results || []
      }
    });
  } catch (error) {
    console.error('Business insights error:', error);
    return c.json({ success: false, message: 'Eroare la încărcarea analizelor de business' }, 500);
  }
});

export { dashboardRoutes };