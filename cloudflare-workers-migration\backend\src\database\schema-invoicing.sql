-- Romanian Invoicing Software Database Schema
-- Compliant with Romanian fiscal laws and e-Factura requirements

-- Companies/Businesses table
CREATE TABLE companies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    cui TEXT NOT NULL UNIQUE, -- Romanian CIF/CUI (tax identification)
    reg_com TEXT, -- Registrul Comertului (Trade Registry)
    address TEXT NOT NULL,
    city TEXT NOT NULL,
    county TEXT NOT NULL,
    postal_code TEXT,
    country TEXT DEFAULT 'România',
    phone TEXT,
    email TEXT,
    bank_account TEXT,
    bank_name TEXT,
    vat_registered BOOLEAN DEFAULT FALSE,
    vat_rate DECIMAL(5,2) DEFAULT 19.00,
    legal_representative TEXT,
    fiscal_representative TEXT,
    authorized_person TEXT,
    logo_url TEXT,
    signature_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Clients table
CREATE TABLE clients (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    cui TEXT, -- Optional for individuals
    reg_com TEXT,
    address TEXT NOT NULL,
    city TEXT NOT NULL,
    county TEXT NOT NULL,
    postal_code TEXT,
    country TEXT DEFAULT 'România',
    phone TEXT,
    email TEXT,
    bank_account TEXT,
    bank_name TEXT,
    vat_registered BOOLEAN DEFAULT FALSE,
    is_individual BOOLEAN DEFAULT FALSE,
    contact_person TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id)
);

-- Products/Services table
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    code TEXT,
    name TEXT NOT NULL,
    description TEXT,
    unit_measure TEXT DEFAULT 'buc', -- bucată, kg, m, etc.
    price DECIMAL(10,4) NOT NULL,
    vat_rate DECIMAL(5,2) DEFAULT 19.00,
    category TEXT,
    barcode TEXT,
    is_service BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id)
);

-- Invoice series (for sequential numbering)
CREATE TABLE invoice_series (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    series TEXT NOT NULL,
    prefix TEXT,
    current_number INTEGER DEFAULT 0,
    year INTEGER,
    active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id),
    UNIQUE(company_id, series, year)
);

-- Invoices table
CREATE TABLE invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    number TEXT NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE,
    delivery_date DATE,
    currency TEXT DEFAULT 'RON',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    
    -- Amounts
    subtotal DECIMAL(12,4) NOT NULL,
    vat_amount DECIMAL(12,4) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,4) DEFAULT 0,
    total DECIMAL(12,4) NOT NULL,
    
    -- Payment info
    payment_method TEXT,
    payment_terms TEXT,
    paid BOOLEAN DEFAULT FALSE,
    paid_date DATE,
    paid_amount DECIMAL(12,4) DEFAULT 0,
    
    -- Additional fields
    notes TEXT,
    internal_notes TEXT,
    delivery_address TEXT,
    
    -- e-Factura fields
    efactura_id TEXT,
    efactura_status TEXT, -- draft, sent, accepted, rejected
    efactura_sent_at DATETIME,
    efactura_xml TEXT,
    
    -- Legal mentions
    reverse_charge BOOLEAN DEFAULT FALSE,
    vat_exempt BOOLEAN DEFAULT FALSE,
    vat_exempt_reason TEXT,
    
    -- Status
    status TEXT DEFAULT 'draft', -- draft, sent, paid, cancelled, overdue
    cancelled BOOLEAN DEFAULT FALSE,
    cancel_reason TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id),
    UNIQUE(company_id, series_id, number)
);

-- Invoice items table
CREATE TABLE invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_id INTEGER NOT NULL,
    product_id INTEGER,
    name TEXT NOT NULL,
    description TEXT,
    quantity DECIMAL(10,4) NOT NULL,
    unit_measure TEXT DEFAULT 'buc',
    unit_price DECIMAL(10,4) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,4) DEFAULT 0,
    subtotal DECIMAL(12,4) NOT NULL,
    vat_rate DECIMAL(5,2) NOT NULL,
    vat_amount DECIMAL(12,4) NOT NULL,
    total DECIMAL(12,4) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Proforma invoices (similar structure to invoices but separate)
CREATE TABLE proforma_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    number TEXT NOT NULL,
    issue_date DATE NOT NULL,
    validity_date DATE,
    currency TEXT DEFAULT 'RON',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    
    -- Amounts
    subtotal DECIMAL(12,4) NOT NULL,
    vat_amount DECIMAL(12,4) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,4) DEFAULT 0,
    total DECIMAL(12,4) NOT NULL,
    
    -- Additional fields
    notes TEXT,
    delivery_address TEXT,
    
    -- Status
    status TEXT DEFAULT 'draft',
    converted_to_invoice_id INTEGER,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id),
    FOREIGN KEY (converted_to_invoice_id) REFERENCES invoices(id),
    UNIQUE(company_id, series_id, number)
);

-- Proforma invoice items
CREATE TABLE proforma_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proforma_id INTEGER NOT NULL,
    product_id INTEGER,
    name TEXT NOT NULL,
    description TEXT,
    quantity DECIMAL(10,4) NOT NULL,
    unit_measure TEXT DEFAULT 'buc',
    unit_price DECIMAL(10,4) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,4) DEFAULT 0,
    subtotal DECIMAL(12,4) NOT NULL,
    vat_rate DECIMAL(5,2) NOT NULL,
    vat_amount DECIMAL(12,4) NOT NULL,
    total DECIMAL(12,4) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    
    FOREIGN KEY (proforma_id) REFERENCES proforma_invoices(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Delivery notes (Avize de însoţire)
CREATE TABLE delivery_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    number TEXT NOT NULL,
    issue_date DATE NOT NULL,
    delivery_date DATE,
    vehicle_number TEXT,
    driver_name TEXT,
    transport_type TEXT,
    
    notes TEXT,
    status TEXT DEFAULT 'draft',
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id),
    UNIQUE(company_id, series_id, number)
);

-- Delivery note items
CREATE TABLE delivery_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_id INTEGER NOT NULL,
    product_id INTEGER,
    name TEXT NOT NULL,
    quantity DECIMAL(10,4) NOT NULL,
    unit_measure TEXT DEFAULT 'buc',
    notes TEXT,
    sort_order INTEGER DEFAULT 0,
    
    FOREIGN KEY (delivery_id) REFERENCES delivery_notes(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Receipts (Chitanţe)
CREATE TABLE receipts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER,
    invoice_id INTEGER,
    series_id INTEGER NOT NULL,
    number TEXT NOT NULL,
    issue_date DATE NOT NULL,
    amount DECIMAL(12,4) NOT NULL,
    currency TEXT DEFAULT 'RON',
    payment_method TEXT,
    description TEXT,
    received_from TEXT,
    notes TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id),
    UNIQUE(company_id, series_id, number)
);

-- Settings table
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    key TEXT NOT NULL,
    value TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    UNIQUE(company_id, key)
);

-- Users table for multi-user support
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    email TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    name TEXT NOT NULL,
    role TEXT DEFAULT 'user', -- admin, user, viewer
    active BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id)
);

-- Audit log for tracking changes
CREATE TABLE audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    user_id INTEGER,
    table_name TEXT NOT NULL,
    record_id INTEGER NOT NULL,
    action TEXT NOT NULL, -- insert, update, delete
    old_values TEXT, -- JSON
    new_values TEXT, -- JSON
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create indexes for better performance
CREATE INDEX idx_invoices_company_date ON invoices(company_id, issue_date);
CREATE INDEX idx_invoices_client ON invoices(client_id);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoice_items_invoice ON invoice_items(invoice_id);
CREATE INDEX idx_clients_company ON clients(company_id);
CREATE INDEX idx_products_company ON products(company_id);
CREATE INDEX idx_users_company ON users(company_id);
CREATE INDEX idx_audit_company_table ON audit_log(company_id, table_name);

-- Insert default VAT rates for Romania
INSERT INTO settings (company_id, key, value) VALUES 
(1, 'vat_rate_standard', '19.00'),
(1, 'vat_rate_reduced_1', '9.00'),
(1, 'vat_rate_reduced_2', '5.00'),
(1, 'currency_default', 'RON'),
(1, 'invoice_due_days', '30'),
(1, 'company_logo_url', ''),
(1, 'company_signature_url', ''),
(1, 'efactura_enabled', 'true'),
(1, 'email_from_address', ''),
(1, 'email_from_name', ''); 