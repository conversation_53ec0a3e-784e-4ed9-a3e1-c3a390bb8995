import type { Env } from '../index';
import { EmailService } from './email';

export interface RecurringInvoice {
  id: number;
  company_id: number;
  client_id: number;
  series_id: number;
  template_name: string;
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  interval_count: number; // e.g., every 2 months
  start_date: string;
  end_date?: string;
  next_generation_date: string;
  last_generated_date?: string;
  is_active: boolean;
  auto_send_email: boolean;
  items: RecurringInvoiceItem[];
  notes?: string;
  payment_terms?: string;
}

export interface RecurringInvoiceItem {
  product_id?: number;
  name: string;
  description?: string;
  quantity: number;
  unit: string;
  unit_price: number;
  vat_rate: number;
}

export class RecurringInvoiceService {
  private env: Env;
  private emailService: EmailService;

  constructor(env: Env) {
    this.env = env;
    this.emailService = new EmailService(env);
  }

  // Create a new recurring invoice template
  async createRecurringInvoice(data: Omit<RecurringInvoice, 'id'>): Promise<number> {
    try {
      // Insert recurring invoice
      const result = await this.env.DB.prepare(`
        INSERT INTO recurring_invoices (
          company_id, client_id, series_id, template_name, frequency, 
          interval_count, start_date, end_date, next_generation_date,
          is_active, auto_send_email, notes, payment_terms
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        data.company_id,
        data.client_id,
        data.series_id,
        data.template_name,
        data.frequency,
        data.interval_count,
        data.start_date,
        data.end_date,
        data.next_generation_date,
        data.is_active ? 1 : 0,
        data.auto_send_email ? 1 : 0,
        data.notes,
        data.payment_terms
      ).run();

      const recurringId = result.meta.last_row_id as number;

      // Insert recurring invoice items
      for (const item of data.items) {
        await this.env.DB.prepare(`
          INSERT INTO recurring_invoice_items (
            recurring_id, product_id, name, description, quantity,
            unit, unit_price, vat_rate
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          recurringId,
          item.product_id,
          item.name,
          item.description,
          item.quantity,
          item.unit,
          item.unit_price,
          item.vat_rate
        ).run();
      }

      return recurringId;
    } catch (error) {
      console.error('Error creating recurring invoice:', error);
      throw error;
    }
  }

  // Process all due recurring invoices
  async processDueRecurringInvoices(): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];

      // Get all active recurring invoices that are due
      const dueRecurring = await this.env.DB.prepare(`
        SELECT * FROM recurring_invoices 
        WHERE is_active = 1 
          AND next_generation_date <= ?
          AND (end_date IS NULL OR end_date >= ?)
      `).bind(today, today).all();

      for (const recurring of dueRecurring.results) {
        await this.generateInvoiceFromRecurring(recurring);
      }
    } catch (error) {
      console.error('Error processing recurring invoices:', error);
    }
  }

  // Generate a single invoice from recurring template
  async generateInvoiceFromRecurring(recurring: any): Promise<number | null> {
    try {
      // Get recurring invoice items
      const items = await this.env.DB.prepare(`
        SELECT * FROM recurring_invoice_items WHERE recurring_id = ?
      `).bind(recurring.id).all();

      // Get next invoice number
      const series = await this.env.DB.prepare(`
        SELECT * FROM invoice_series WHERE id = ?
      `).bind(recurring.series_id).first();

      if (!series) {
        throw new Error('Invoice series not found');
      }

      const nextNumber = (series.current_number + 1).toString().padStart(4, '0');

      // Calculate totals
      let subtotal = 0;
      let vatAmount = 0;
      
      for (const item of items.results) {
        const itemSubtotal = item.quantity * item.unit_price;
        const itemVat = itemSubtotal * (item.vat_rate / 100);
        subtotal += itemSubtotal;
        vatAmount += itemVat;
      }

      const total = subtotal + vatAmount;
      const issueDate = new Date().toISOString().split('T')[0];
      const dueDate = this.calculateDueDate(issueDate, recurring.payment_terms);

      // Create invoice
      const invoiceResult = await this.env.DB.prepare(`
        INSERT INTO invoices (
          company_id, client_id, series_id, number, issue_date, due_date,
          currency, subtotal, vat_amount, total, notes, recurring_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        recurring.company_id,
        recurring.client_id,
        recurring.series_id,
        nextNumber,
        issueDate,
        dueDate,
        'RON',
        subtotal,
        vatAmount,
        total,
        recurring.notes,
        recurring.id
      ).run();

      const invoiceId = invoiceResult.meta.last_row_id as number;

      // Create invoice items
      for (const item of items.results) {
        const itemSubtotal = item.quantity * item.unit_price;
        const itemVat = itemSubtotal * (item.vat_rate / 100);
        const itemTotal = itemSubtotal + itemVat;

        await this.env.DB.prepare(`
          INSERT INTO invoice_items (
            invoice_id, product_id, name, description, quantity, unit,
            unit_price, subtotal, vat_rate, vat_amount, total
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          invoiceId,
          item.product_id,
          item.name,
          item.description,
          item.quantity,
          item.unit,
          item.unit_price,
          itemSubtotal,
          item.vat_rate,
          itemVat,
          itemTotal
        ).run();
      }

      // Update invoice series number
      await this.env.DB.prepare(`
        UPDATE invoice_series SET current_number = ? WHERE id = ?
      `).bind(series.current_number + 1, series.id).run();

      // Update recurring invoice
      const nextGenerationDate = this.calculateNextGenerationDate(
        recurring.next_generation_date,
        recurring.frequency,
        recurring.interval_count
      );

      await this.env.DB.prepare(`
        UPDATE recurring_invoices 
        SET last_generated_date = ?, next_generation_date = ?
        WHERE id = ?
      `).bind(issueDate, nextGenerationDate, recurring.id).run();

      // Send email if auto-send is enabled
      if (recurring.auto_send_email) {
        await this.sendRecurringInvoiceEmail(invoiceId);
      }

      return invoiceId;
    } catch (error) {
      console.error('Error generating invoice from recurring:', error);
      return null;
    }
  }

  // Calculate next generation date based on frequency
  private calculateNextGenerationDate(
    currentDate: string,
    frequency: string,
    intervalCount: number
  ): string {
    const date = new Date(currentDate);

    switch (frequency) {
      case 'weekly':
        date.setDate(date.getDate() + (7 * intervalCount));
        break;
      case 'monthly':
        date.setMonth(date.getMonth() + intervalCount);
        break;
      case 'quarterly':
        date.setMonth(date.getMonth() + (3 * intervalCount));
        break;
      case 'yearly':
        date.setFullYear(date.getFullYear() + intervalCount);
        break;
    }

    return date.toISOString().split('T')[0];
  }

  // Calculate due date based on payment terms
  private calculateDueDate(issueDate: string, paymentTerms?: string): string {
    const date = new Date(issueDate);
    
    if (paymentTerms) {
      const daysMatch = paymentTerms.match(/(\d+)\s*zile?/i);
      if (daysMatch) {
        const days = parseInt(daysMatch[1]);
        date.setDate(date.getDate() + days);
      }
    } else {
      // Default to 30 days
      date.setDate(date.getDate() + 30);
    }

    return date.toISOString().split('T')[0];
  }

  // Send email for generated recurring invoice
  private async sendRecurringInvoiceEmail(invoiceId: number): Promise<void> {
    try {
      // Get invoice details
      const invoice = await this.env.DB.prepare(`
        SELECT i.*, c.name as client_name, c.email as client_email,
               comp.name as company_name
        FROM invoices i
        JOIN clients c ON i.client_id = c.id
        JOIN companies comp ON i.company_id = comp.id
        WHERE i.id = ?
      `).bind(invoiceId).first();

      if (invoice && invoice.client_email) {
        await this.emailService.sendInvoiceEmail(
          invoice,
          { name: invoice.client_name, email: invoice.client_email },
          { name: invoice.company_name }
        );
      }
    } catch (error) {
      console.error('Error sending recurring invoice email:', error);
    }
  }

  // Get all recurring invoices for a company
  async getRecurringInvoices(companyId: number): Promise<any[]> {
    try {
      const recurring = await this.env.DB.prepare(`
        SELECT 
          ri.*,
          c.name as client_name,
          s.series as series_name
        FROM recurring_invoices ri
        JOIN clients c ON ri.client_id = c.id
        JOIN invoice_series s ON ri.series_id = s.id
        WHERE ri.company_id = ?
        ORDER BY ri.created_at DESC
      `).bind(companyId).all();

      return recurring.results || [];
    } catch (error) {
      console.error('Error getting recurring invoices:', error);
      return [];
    }
  }

  // Update recurring invoice
  async updateRecurringInvoice(
    id: number,
    data: Partial<RecurringInvoice>
  ): Promise<boolean> {
    try {
      const updates: string[] = [];
      const values: any[] = [];

      if (data.template_name !== undefined) {
        updates.push('template_name = ?');
        values.push(data.template_name);
      }
      if (data.frequency !== undefined) {
        updates.push('frequency = ?');
        values.push(data.frequency);
      }
      if (data.interval_count !== undefined) {
        updates.push('interval_count = ?');
        values.push(data.interval_count);
      }
      if (data.is_active !== undefined) {
        updates.push('is_active = ?');
        values.push(data.is_active ? 1 : 0);
      }
      if (data.auto_send_email !== undefined) {
        updates.push('auto_send_email = ?');
        values.push(data.auto_send_email ? 1 : 0);
      }
      if (data.end_date !== undefined) {
        updates.push('end_date = ?');
        values.push(data.end_date);
      }

      if (updates.length === 0) {
        return true;
      }

      values.push(id);

      await this.env.DB.prepare(`
        UPDATE recurring_invoices 
        SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(...values).run();

      return true;
    } catch (error) {
      console.error('Error updating recurring invoice:', error);
      return false;
    }
  }

  // Delete recurring invoice
  async deleteRecurringInvoice(id: number): Promise<boolean> {
    try {
      // Delete items first
      await this.env.DB.prepare(`
        DELETE FROM recurring_invoice_items WHERE recurring_id = ?
      `).bind(id).run();

      // Delete recurring invoice
      await this.env.DB.prepare(`
        DELETE FROM recurring_invoices WHERE id = ?
      `).bind(id).run();

      return true;
    } catch (error) {
      console.error('Error deleting recurring invoice:', error);
      return false;
    }
  }
}
