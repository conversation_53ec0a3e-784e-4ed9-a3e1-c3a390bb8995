name = "handmadein-admin"
compatibility_date = "2024-01-01"
workers_dev = true

# Static assets configuration
[assets]
directory = "dist"
not_found_handling = "single-page-application"

# Environment variables for default environment (development/staging)
[vars]
VITE_API_URL = "https://bapi.handmadein.ro"
VITE_APP_ENV = "production"

# Production environment
[env.production]
vars = { VITE_API_URL = "https://bapi.handmadein.ro", VITE_APP_ENV = "production" }

# Staging environment (explicit)
[env.staging]
vars = { VITE_API_URL = "https://bapi.handmadein.ro", VITE_APP_ENV = "staging" } 