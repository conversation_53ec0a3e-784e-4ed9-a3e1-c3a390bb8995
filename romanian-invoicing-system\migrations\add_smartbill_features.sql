-- Migration to add Smartbill-compatible features
-- Run this to add all the new tables and columns

-- Recurring invoices table
CREATE TABLE IF NOT EXISTS recurring_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    template_name TEXT NOT NULL,
    frequency TEXT NOT NULL CHECK (frequency IN ('weekly', 'monthly', 'quarterly', 'yearly')),
    interval_count INTEGER NOT NULL DEFAULT 1,
    start_date DATE NOT NULL,
    end_date DATE,
    next_generation_date DATE NOT NULL,
    last_generated_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    auto_send_email BOOLEAN DEFAULT FALSE,
    notes TEXT,
    payment_terms TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id)
);

-- Recurring invoice items table
CREATE TABLE IF NOT EXISTS recurring_invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    recurring_id INTEGER NOT NULL,
    product_id INTEGER,
    name TEXT NOT NULL,
    description TEXT,
    quantity DECIMAL(10,4) NOT NULL,
    unit TEXT NOT NULL DEFAULT 'buc',
    unit_price DECIMAL(10,4) NOT NULL,
    vat_rate DECIMAL(5,2) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    
    FOREIGN KEY (recurring_id) REFERENCES recurring_invoices(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Payment reminders table
CREATE TABLE IF NOT EXISTS payment_reminders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    invoice_id INTEGER NOT NULL,
    reminder_type TEXT NOT NULL CHECK (reminder_type IN ('gentle', 'firm', 'final')),
    sent_date DATETIME NOT NULL,
    email_sent BOOLEAN DEFAULT FALSE,
    notes TEXT,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id)
);

-- Email templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    template_type TEXT NOT NULL CHECK (template_type IN ('invoice', 'reminder', 'welcome', 'custom')),
    name TEXT NOT NULL,
    subject TEXT NOT NULL,
    html_content TEXT NOT NULL,
    text_content TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id)
);

-- Inventory tracking table
CREATE TABLE IF NOT EXISTS inventory_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('in', 'out', 'adjustment')),
    quantity DECIMAL(10,4) NOT NULL,
    reference_type TEXT, -- 'invoice', 'manual', 'purchase'
    reference_id INTEGER,
    notes TEXT,
    transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Payment transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_id INTEGER NOT NULL,
    payment_provider TEXT NOT NULL,
    payment_id TEXT NOT NULL,
    amount DECIMAL(12,4) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'RON',
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    webhook_data TEXT, -- JSON data from payment provider
    notes TEXT,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id)
);

-- Add new columns to existing tables (use ALTER TABLE ADD COLUMN if not exists)

-- Add recurring_id to invoices table
ALTER TABLE invoices ADD COLUMN recurring_id INTEGER REFERENCES recurring_invoices(id);

-- Add stock tracking to products table
ALTER TABLE products ADD COLUMN track_stock BOOLEAN DEFAULT FALSE;
ALTER TABLE products ADD COLUMN current_stock DECIMAL(10,4) DEFAULT 0;
ALTER TABLE products ADD COLUMN min_stock_level DECIMAL(10,4) DEFAULT 0;

-- Add payment fields to invoices table
ALTER TABLE invoices ADD COLUMN paid_amount DECIMAL(12,4);
ALTER TABLE invoices ADD COLUMN payment_method TEXT;
ALTER TABLE invoices ADD COLUMN paid_date DATE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_recurring_invoices_company ON recurring_invoices(company_id);
CREATE INDEX IF NOT EXISTS idx_recurring_invoices_next_date ON recurring_invoices(next_generation_date);
CREATE INDEX IF NOT EXISTS idx_recurring_items_recurring ON recurring_invoice_items(recurring_id);
CREATE INDEX IF NOT EXISTS idx_payment_reminders_invoice ON payment_reminders(invoice_id);
CREATE INDEX IF NOT EXISTS idx_email_templates_company ON email_templates(company_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_product ON inventory_transactions(product_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_invoice ON payment_transactions(invoice_id);

-- Insert additional settings for new features
INSERT OR IGNORE INTO settings (company_id, key, value) VALUES 
(1, 'auto_send_invoices', 'false'),
(1, 'payment_reminder_days', '7,14,30'),
(1, 'auto_payment_reminders', 'false'),
(1, 'inventory_tracking', 'false'),
(1, 'stripe_enabled', 'false'),
(1, 'paypal_enabled', 'false'),
(1, 'recurring_invoices_enabled', 'true'),
(1, 'email_notifications_enabled', 'true');

-- Insert default email templates
INSERT OR IGNORE INTO email_templates (company_id, template_type, name, subject, html_content, text_content, is_default, is_active) VALUES
(1, 'invoice', 'Default Invoice Template', 'Factură {{invoice_number}} - {{company_name}}', 
'<h1>Factură {{invoice_number}}</h1><p>Bună ziua {{client_name}},</p><p>Vă transmitem în atașament factura cu numărul {{invoice_number}}.</p>',
'Factură {{invoice_number}}\n\nBună ziua {{client_name}},\n\nVă transmitem în atașament factura cu numărul {{invoice_number}}.',
TRUE, TRUE),

(1, 'reminder', 'Default Reminder Template', 'Reamintire plată - Factură {{invoice_number}}',
'<h1>Reamintire plată</h1><p>Bună ziua {{client_name}},</p><p>Vă reamintim că factura {{invoice_number}} nu a fost încă achitată.</p>',
'Reamintire plată\n\nBună ziua {{client_name}},\n\nVă reamintim că factura {{invoice_number}} nu a fost încă achitată.',
TRUE, TRUE),

(1, 'welcome', 'Default Welcome Template', 'Bun venit în familia {{company_name}}!',
'<h1>Bun venit!</h1><p>Bună ziua {{client_name}},</p><p>Vă mulțumim că ați ales să colaborați cu {{company_name}}!</p>',
'Bun venit!\n\nBună ziua {{client_name}},\n\nVă mulțumim că ați ales să colaborați cu {{company_name}}!',
TRUE, TRUE);
