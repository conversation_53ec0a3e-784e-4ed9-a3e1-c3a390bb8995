const fs = require('fs');
const path = require('path');

// This script bulk uploads the migrated reviews data to Cloudflare KV
// It reads the generated kv-operations.json and uploads in batches

async function bulkUploadToKV() {
  const kvOperationsFile = path.join(__dirname, '../migration-data/kv-operations.json');
  
  if (!fs.existsSync(kvOperationsFile)) {
    console.error('KV operations file not found. Run migrate:reviews first.');
    process.exit(1);
  }
  
  const kvOperations = JSON.parse(fs.readFileSync(kvOperationsFile, 'utf8'));
  console.log(`Loaded ${kvOperations.length} KV operations for upload`);
  
  // Get environment variables
  const CLOUDFLARE_ACCOUNT_ID = process.env.CLOUDFLARE_ACCOUNT_ID;
  const CLOUDFLARE_API_TOKEN = process.env.CLOUDFLARE_API_TOKEN;
  const KV_NAMESPACE_ID = process.env.REVIEWS_KV_NAMESPACE_ID;
  
  if (!CLOUDFLARE_ACCOUNT_ID || !CLOUDFLARE_API_TOKEN || !KV_NAMESPACE_ID) {
    console.error('Missing required environment variables:');
    console.error('- CLOUDFLARE_ACCOUNT_ID');
    console.error('- CLOUDFLARE_API_TOKEN');
    console.error('- REVIEWS_KV_NAMESPACE_ID');
    console.error('\nPlease set these in your environment or .env file');
    process.exit(1);
  }
  
  const BATCH_SIZE = 100; // Cloudflare KV API limit is 10,000 keys per batch, but we'll use smaller batches
  const batches = [];
  
  // Split operations into batches
  for (let i = 0; i < kvOperations.length; i += BATCH_SIZE) {
    batches.push(kvOperations.slice(i, i + BATCH_SIZE));
  }
  
  console.log(`Uploading in ${batches.length} batches of max ${BATCH_SIZE} keys each...`);
  
  let totalUploaded = 0;
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    
    console.log(`Uploading batch ${i + 1}/${batches.length} (${batch.length} keys)...`);
    
    try {
      // Prepare bulk upload data
      const bulkData = batch.map(op => ({
        key: op.key,
        value: op.value
      }));
      
      // Make API request to Cloudflare KV
      const response = await fetch(
        `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/storage/kv/namespaces/${KV_NAMESPACE_ID}/bulk`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(bulkData)
        }
      );
      
      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Batch ${i + 1} failed: ${response.status} ${response.statusText}\n${errorData}`);
      }
      
      const responseData = await response.json();
      
      if (!responseData.success) {
        throw new Error(`Batch ${i + 1} failed: ${JSON.stringify(responseData.errors)}`);
      }
      
      totalUploaded += batch.length;
      console.log(`✓ Batch ${i + 1} uploaded successfully (${totalUploaded}/${kvOperations.length} total)`);
      
      // Small delay between batches to avoid rate limiting
      if (i < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
    } catch (error) {
      console.error(`✗ Batch ${i + 1} failed:`, error.message);
      process.exit(1);
    }
  }
  
  console.log(`\n✅ Successfully uploaded all ${totalUploaded} keys to KV storage!`);
  
  // Generate summary
  const summary = {
    timestamp: new Date().toISOString(),
    total_uploaded: totalUploaded,
    batches: batches.length,
    reviews: kvOperations.filter(op => op.type === 'review').length,
    product_reviews: kvOperations.filter(op => op.type === 'product_reviews').length,
    customer_reviews: kvOperations.filter(op => op.type === 'customer_reviews').length,
    product_ratings: kvOperations.filter(op => op.type === 'product_rating').length
  };
  
  const summaryFile = path.join(__dirname, '../migration-data/upload-summary.json');
  fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
  console.log(`Upload summary written to: ${summaryFile}`);
}

// Handle fetch polyfill for Node.js < 18
if (typeof fetch === 'undefined') {
  global.fetch = require('node-fetch');
}

bulkUploadToKV().catch(error => {
  console.error('Upload failed:', error);
  process.exit(1);
}); 