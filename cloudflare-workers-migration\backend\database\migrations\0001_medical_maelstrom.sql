CREATE TABLE `order_items` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`order_id` text NOT NULL,
	`variant_id` text,
	`product_title` text NOT NULL,
	`variant_title` text,
	`sku` text,
	`quantity` integer NOT NULL,
	`unit_price` real NOT NULL,
	`total_price` real NOT NULL,
	`fulfilled_quantity` integer DEFAULT 0,
	`refunded_quantity` integer DEFAULT 0,
	`metadata` text
);
--> statement-breakpoint
CREATE TABLE `product_translations` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`deleted_at` text,
	`product_id` text NOT NULL,
	`language_code` text NOT NULL,
	`title` text NOT NULL,
	`description` text
);
--> statement-breakpoint
DROP TABLE `line_items`;--> statement-breakpoint
ALTER TABLE `products` DROP COLUMN `title`;