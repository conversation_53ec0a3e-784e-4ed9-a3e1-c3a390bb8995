{"name": "handmadein-admin", "version": "1.0.0", "description": "Admin interface for HandmadeIn.ro", "main": "src/index.tsx", "scripts": {"dev": "vite", "build": "vite build", "build:staging": "VITE_APP_ENV=staging VITE_API_URL=https://bapi.handmadein.ro npm run build", "build:production": "VITE_APP_ENV=production VITE_API_URL=https://bapi.handmadein.ro npm run build", "preview": "vite preview", "deploy": "npm run build && wrangler deploy", "deploy:staging": "npm run build:staging && wrangler deploy --env staging", "deploy:production": "npm run build:production && wrangler deploy --env production", "wrangler:dev": "wrangler dev --port 8080", "type-check": "tsc --noEmit", "type-check:build": "tsc && vite build", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@tanstack/react-query": "^5.0.0", "@tanstack/react-query-devtools": "^5.0.0", "@tanstack/react-table": "^8.10.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "tailwindcss": "^3.3.0", "clsx": "^2.0.0", "date-fns": "^3.0.0", "recharts": "^2.8.0", "react-hook-form": "^7.45.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "axios": "^1.5.0", "react-hot-toast": "^2.4.0", "handmadein-shared": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "vite": "^4.4.0", "typescript": "^5.1.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "wrangler": "^3.57.0"}, "engines": {"node": ">=18.0.0"}}