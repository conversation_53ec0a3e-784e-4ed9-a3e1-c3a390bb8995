import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  ArrowLeftIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  PencilIcon,
  PrinterIcon,
  EnvelopeIcon,
  UserIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  DocumentTextIcon,
  PlusIcon,
  EyeIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';

interface Order {
  id: string;
  number: string;
  customer_id?: string;
  email: string;
  currency_code: string;
  region_id: string;
  status: string;
  financial_status: string;
  fulfillment_status: string;
  subtotal: number;
  tax_amount: number;
  shipping_amount: number;
  discount_amount: number;
  total_amount: number;
  billing_address: Address;
  shipping_address: Address;
  notes?: string;
  metadata?: Record<string, any> & {
    easybox_locker_id?: number;
    easybox_locker_name?: string;
    easybox_locker_address?: string;
    easybox_locker_city?: string;
    easybox_locker_county?: string;
    easybox_delivery?: {
      locker_id: number;
      locker_name: string;
      locker_address: string;
      locker_city: string;
      locker_county: string;
      full_address: string;
    };
    shipping_method?: {
      id: string;
      name: string;
      price: number;
      currency_code: string;
      estimated_delivery_days?: number;
      provider: string;
      data?: any;
      selected_at: string;
    };
    tracking_number?: string;
    tracking_url?: string;
    courier?: string;
    shipped_at?: string;
  };
  easybox_delivery?: {
    locker_id: number;
    locker_name: string;
    locker_address: string;
    locker_city: string;
    locker_county: string;
    full_address: string;
  } | null;
  is_easybox_delivery?: boolean;
  customer?: Customer;
  items: OrderItem[];
  payments: Payment[];
  fulfillments: Fulfillment[];
  created_at: string;
  updated_at: string;
  cancelled_at?: string;
}

interface Customer {
  id: string;
  first_name?: string;
  last_name?: string;
  email: string;
  phone?: string;
}

interface Address {
  first_name?: string;
  last_name?: string;
  company?: string;
  address_1?: string;
  address_2?: string;
  address_line_1?: string;
  address_line_2?: string;
  city: string;
  state?: string;
  province?: string;
  postal_code: string;
  country_code: string;
  phone?: string;
}

interface OrderItem {
  id: string;
  variant_id: string;
  product_title: string;
  variant_title: string;
  sku?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  fulfilled_quantity: number;
  refunded_quantity: number;
}

interface Payment {
  id: string;
  provider: string;
  type: string;
  status: string;
  amount: number;
  currency_code: string;
  processed_at?: string;
  created_at: string;
}

interface Fulfillment {
  id: string;
  status: string;
  tracking_number?: string;
  tracking_url?: string;
  carrier?: string;
  shipped_at?: string;
  delivered_at?: string;
  items: FulfillmentItem[];
  created_at: string;
}

interface FulfillmentItem {
  order_item_id: string;
  quantity: number;
}

interface DetectedCourier {
  trackingNumber: string;
  courier: string;
  courierName: string;
  trackingUrl: string;
  isRecognized: boolean;
}

const ORDER_STATUSES = [
  { value: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'confirmed', label: 'Confirmed', color: 'bg-blue-100 text-blue-800' },
  { value: 'processing', label: 'Processing', color: 'bg-indigo-100 text-indigo-800' },
  { value: 'shipped', label: 'Shipped', color: 'bg-purple-100 text-purple-800' },
  { value: 'delivered', label: 'Delivered', color: 'bg-green-100 text-green-800' },
  { value: 'cancelled', label: 'Cancelled', color: 'bg-red-100 text-red-800' },
  { value: 'refunded', label: 'Refunded', color: 'bg-gray-100 text-gray-800' },
];

const OrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [notes, setNotes] = useState('');
  const [showCreateFulfillment, setShowCreateFulfillment] = useState(false);
  const [showTrackingModal, setShowTrackingModal] = useState(false);
  const [trackingForm, setTrackingForm] = useState({
    tracking_number: '',
    courier: '',
    send_email: true,
  });
  const [detectedCourier, setDetectedCourier] = useState<DetectedCourier | null>(null);
  const [isDetectingCourier, setIsDetectingCourier] = useState(false);


  // Fetch order details
  const { data: order, isLoading, error } = useQuery({
    queryKey: ['order', id],
    queryFn: async () => {
      const response = await api.get(`/admin/api/orders/${id}`);
      return response.data.order;
    },
    enabled: !!id,
  });

  // Update order status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async ({ status }: { status: string }) => {
      return api.put(`/admin/api/orders/${id}/status`, { status });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['order', id] });
      toast.success('Order status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update order status');
    },
  });

  // Update notes mutation
  const updateNotesMutation = useMutation({
    mutationFn: async ({ notes }: { notes: string }) => {
      return api.put(`/admin/api/orders/${id}/notes`, { notes });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['order', id] });
      toast.success('Notes updated successfully');
      setIsEditingNotes(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update notes');
    },
  });

  // Add/Update tracking number mutation
  const addTrackingMutation = useMutation({
    mutationFn: async (data: { tracking_number: string; courier?: string; send_email?: boolean }) => {
      return api.post('/admin/shipping/ship', {
        orderId: id,
        trackingNumber: data.tracking_number,
        courier: data.courier,
        sendEmail: data.send_email,
      });
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['order', id] });
      toast.success(response.data.message || 'Tracking number added successfully');
      setShowTrackingModal(false);
      resetTrackingForm();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to add tracking number');
    },
  });

  // Detect courier mutation
  const detectCourierMutation = useMutation({
    mutationFn: async (trackingNumber: string) => {
      return api.post('/admin/shipping/detect-courier', { trackingNumber });
    },
    onSuccess: (response) => {
      const data = response.data.data;
      setDetectedCourier(data);
      setTrackingForm(prev => ({
        ...prev,
        courier: data.courier,
      }));
    },
    onError: (error: any) => {
      toast.error('Failed to detect courier');
    },
  });

  // Create fulfillment mutation
  const createFulfillmentMutation = useMutation({
    mutationFn: async (fulfillmentData: any) => {
      return api.post(`/admin/api/orders/${id}/fulfillments`, fulfillmentData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['order', id] });
      toast.success('Fulfillment created successfully');
      setShowCreateFulfillment(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create fulfillment');
    },
  });



  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const resetTrackingForm = () => {
    setTrackingForm({
      tracking_number: '',
      courier: '',
      send_email: true,
    });
    setDetectedCourier(null);
  };

  const handleTrackingNumberChange = async (trackingNumber: string) => {
    setTrackingForm(prev => ({ ...prev, tracking_number: trackingNumber }));
    
    // Auto-detect courier when user types a tracking number
    if (trackingNumber.length >= 8) {
      setIsDetectingCourier(true);
      try {
        await detectCourierMutation.mutateAsync(trackingNumber);
      } catch (error) {
        // Error handling is done in the mutation
      } finally {
        setIsDetectingCourier(false);
      }
    } else {
      setDetectedCourier(null);
      setTrackingForm(prev => ({ ...prev, courier: '' }));
    }
  };

  const openTrackingModal = (existingTracking?: { tracking_number: string; courier?: string }) => {
    if (existingTracking) {
      setTrackingForm({
        tracking_number: existingTracking.tracking_number,
        courier: existingTracking.courier || '',
        send_email: false, // Don't send email when updating existing tracking
      });
    } else {
      resetTrackingForm();
    }
    setShowTrackingModal(true);
  };

  const handleSubmitTracking = () => {
    if (!trackingForm.tracking_number.trim()) {
      toast.error('Please enter a tracking number');
      return;
    }

    addTrackingMutation.mutate({
      tracking_number: trackingForm.tracking_number.trim(),
      courier: trackingForm.courier || detectedCourier?.courier,
      send_email: trackingForm.send_email,
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Order not found</h2>
          <p className="text-gray-600 mb-4">The order you're looking for doesn't exist.</p>
          <Link to="/orders" className="text-blue-600 hover:text-blue-800">
            ← Back to orders
          </Link>
        </div>
      </div>
    );
  }

  const hasExistingTracking = order.metadata?.tracking_number || 
                            order.fulfillments?.some(f => f.tracking_number);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/orders')}
            className="p-2 text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Order #{order.number}
            </h1>
            <div className="flex items-center space-x-4 mt-1">
              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                ORDER_STATUSES.find(s => s.value === order.status)?.color || 'bg-gray-100 text-gray-800'
              }`}>
                {ORDER_STATUSES.find(s => s.value === order.status)?.label || order.status}
              </span>
              <span className="text-sm text-gray-500">
                {new Date(order.created_at).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <select
            value={order.status}
            onChange={(e) => updateStatusMutation.mutate({ status: e.target.value })}
            className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={updateStatusMutation.isPending}
          >
            {ORDER_STATUSES.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>

          <button className="p-2 text-gray-500 hover:text-gray-700">
            <PrinterIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Tracking & Fulfillment Section */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Tracking & Fulfillment</h3>
              <div className="flex space-x-2">
                <button
                  onClick={() => openTrackingModal()}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2"
                >
                  <TruckIcon className="h-4 w-4" />
                  <span>{hasExistingTracking ? 'Update Tracking' : 'Add Tracking'}</span>
                </button>
                {!showCreateFulfillment && (
                  <button
                    onClick={() => setShowCreateFulfillment(true)}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1"
                  >
                    <PlusIcon className="h-4 w-4" />
                    <span>Create Fulfillment</span>
                  </button>
                )}
              </div>
            </div>
            
            <div className="p-6">
              {/* Current Tracking Information */}
              {(order.metadata?.tracking_number || hasExistingTracking) && (
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-blue-900 mb-2">Current Tracking Information</h4>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-blue-700">Tracking Number:</span>
                          {order.metadata?.tracking_url ? (
                            <a 
                              href={order.metadata.tracking_url} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="text-sm font-medium text-blue-600 hover:underline"
                            >
                              {order.metadata.tracking_number}
                            </a>
                          ) : (
                            <span className="text-sm font-medium text-blue-900">
                              {order.metadata?.tracking_number}
                            </span>
                          )}
                        </div>
                        {order.metadata?.courier && (
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-blue-700">Courier:</span>
                            <span className="text-sm font-medium text-blue-900">
                              {order.metadata.courier}
                            </span>
                          </div>
                        )}
                        {order.metadata?.shipped_at && (
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-blue-700">Shipped:</span>
                            <span className="text-sm font-medium text-blue-900">
                              {new Date(order.metadata.shipped_at).toLocaleString()}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => openTrackingModal({
                        tracking_number: order.metadata?.tracking_number || '',
                        courier: order.metadata?.courier,
                      })}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              )}

              {/* Fulfillments */}
              {order.fulfillments && order.fulfillments.length > 0 ? (
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-gray-900">Fulfillments</h4>
                  {order.fulfillments.map((fulfillment: Fulfillment) => (
                    <div key={fulfillment.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <TruckIcon className="h-5 w-5 text-gray-400" />
                          <span className="font-medium text-gray-900">
                            Fulfillment #{fulfillment.id.slice(-6)}
                          </span>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                            fulfillment.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                            fulfillment.status === 'delivered' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {fulfillment.status}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(fulfillment.created_at).toLocaleDateString()}
                        </div>
                      </div>
                      
                      {fulfillment.tracking_number && (
                        <div className="text-sm text-gray-600 flex items-center space-x-2">
                          <span>Tracking:</span>
                          {fulfillment.tracking_url ? (
                            <a 
                              href={fulfillment.tracking_url} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="text-blue-600 hover:underline font-medium"
                            >
                              {fulfillment.tracking_number}
                            </a>
                          ) : (
                            <span className="font-medium">{fulfillment.tracking_number}</span>
                          )}
                        </div>
                      )}
                      {fulfillment.carrier && (
                        <div className="text-sm text-gray-600">
                          <span>Courier: </span>
                          <span className="font-medium">{fulfillment.carrier}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <TruckIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No fulfillments</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Add tracking information to start shipping this order.
                  </p>
                </div>
              )}

              {/* Create Fulfillment Form */}
              {showCreateFulfillment && (
                <div className="mt-6 p-4 bg-gray-50 border rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Create New Fulfillment</h4>
                  <div className="space-y-3">
                    <button
                      onClick={() => {
                        createFulfillmentMutation.mutate({
                          tracking_number: order.metadata?.tracking_number,
                          tracking_url: order.metadata?.tracking_url,
                          carrier: order.metadata?.courier,
                          items: order.items.map(item => ({
                            order_item_id: item.id,
                            quantity: item.quantity - item.fulfilled_quantity,
                          })).filter(item => item.quantity > 0),
                        });
                      }}
                      disabled={createFulfillmentMutation.isPending}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:bg-blue-400"
                    >
                      {createFulfillmentMutation.isPending ? 'Creating...' : 'Create Fulfillment'}
                    </button>
                    <button
                      onClick={() => setShowCreateFulfillment(false)}
                      className="w-full text-gray-600 hover:text-gray-800 text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Order Items */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Items</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {order.items.map((item: OrderItem) => (
                  <div key={item.id} className="flex items-center space-x-4 py-4 border-b border-gray-200 last:border-b-0">
                    <div className="h-16 w-16 bg-gray-100 rounded-lg flex items-center justify-center">
                      <ShoppingBagIcon className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.product_title}</h4>
                      {item.variant_title && (
                        <p className="text-sm text-gray-600">{item.variant_title}</p>
                      )}
                      {item.sku && (
                        <p className="text-xs text-gray-500">SKU: {item.sku}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        {formatCurrency(item.unit_price, order.currency_code)}
                      </p>
                      <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                      <p className="text-xs text-gray-500">
                        Total: {formatCurrency(item.total_price, order.currency_code)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Notes</h3>
              <button
                onClick={() => {
                  setNotes(order.notes || '');
                  setIsEditingNotes(true);
                }}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                <PencilIcon className="h-4 w-4 inline mr-1" />
                Edit
              </button>
            </div>
            <div className="p-6">
              {isEditingNotes ? (
                <div className="space-y-3">
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={4}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Add notes about this order..."
                  />
                  <div className="flex space-x-2">
                    <button
                      onClick={() => updateNotesMutation.mutate({ notes })}
                      disabled={updateNotesMutation.isPending}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:bg-blue-400"
                    >
                      {updateNotesMutation.isPending ? 'Saving...' : 'Save'}
                    </button>
                    <button
                      onClick={() => setIsEditingNotes(false)}
                      className="text-gray-600 hover:text-gray-800 px-4 py-2 text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <div>
                  {order.notes ? (
                    <p className="text-gray-900 whitespace-pre-wrap">{order.notes}</p>
                  ) : (
                    <p className="text-gray-500 italic">No notes added</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Customer</h3>
            </div>
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                  <UserIcon className="h-5 w-5 text-gray-400" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {order.customer?.first_name && order.customer?.last_name 
                      ? `${order.customer.first_name} ${order.customer.last_name}`
                      : 'Guest Customer'
                    }
                  </p>
                  <p className="text-sm text-gray-600">{order.email}</p>
                </div>
              </div>
              {order.customer_id && (
                <Link
                  to={`/customers/${order.customer_id}`}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  View customer profile →
                </Link>
              )}
            </div>
          </div>

          {/* Shipping Address */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Shipping Address</h3>
            </div>
            <div className="p-6">
              <div className="flex items-start space-x-3">
                <MapPinIcon className="h-5 w-5 text-gray-400 mt-1" />
                <div className="text-sm text-gray-900">
                  {order.is_easybox_delivery && order.easybox_delivery ? (
                    <div>
                      <div className="font-medium text-orange-600 mb-2">📦 EasyBox Delivery</div>
                      <div>{order.shipping_address.first_name} {order.shipping_address.last_name}</div>
                      <div className="font-medium">{order.easybox_delivery.locker_name}</div>
                      <div>{order.easybox_delivery.locker_address}</div>
                      <div>{order.easybox_delivery.locker_city}, {order.easybox_delivery.locker_county}</div>
                    </div>
                  ) : (
                    <div>
                      <div>{order.shipping_address.first_name} {order.shipping_address.last_name}</div>
                      {order.shipping_address.company && <div>{order.shipping_address.company}</div>}
                      <div>{order.shipping_address.address_line_1}</div>
                      {order.shipping_address.address_line_2 && <div>{order.shipping_address.address_line_2}</div>}
                      <div>{order.shipping_address.postal_code} {order.shipping_address.city}</div>
                      {order.shipping_address.state && <div>{order.shipping_address.state}</div>}
                      <div>{order.shipping_address.country_code}</div>
                    </div>
                  )}
                  {order.shipping_address.phone && (
                    <div className="mt-2 text-gray-600">📞 {order.shipping_address.phone}</div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Order Summary</h3>
            </div>
            <div className="p-6">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="text-gray-900">{formatCurrency(order.subtotal, order.currency_code)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span className="text-gray-900">{formatCurrency(order.shipping_amount, order.currency_code)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax</span>
                  <span className="text-gray-900">{formatCurrency(order.tax_amount, order.currency_code)}</span>
                </div>
                {order.discount_amount > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Discount</span>
                    <span className="text-green-600">-{formatCurrency(order.discount_amount, order.currency_code)}</span>
                  </div>
                )}
                <div className="flex justify-between font-medium text-base pt-2 border-t border-gray-200">
                  <span className="text-gray-900">Total</span>
                  <span className="text-gray-900">{formatCurrency(order.total_amount, order.currency_code)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          {order.payments && order.payments.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Payment Details</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {order.payments.map((payment: Payment) => (
                    <div key={payment.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-full ${
                            payment.provider === 'stripe' ? 'bg-purple-100' :
                            payment.provider === 'paypal' ? 'bg-blue-100' :
                            payment.provider === 'manual' ? 'bg-gray-100' :
                            'bg-green-100'
                          }`}>
                            <CurrencyDollarIcon className={`h-4 w-4 ${
                              payment.provider === 'stripe' ? 'text-purple-600' :
                              payment.provider === 'paypal' ? 'text-blue-600' :
                              payment.provider === 'manual' ? 'text-gray-600' :
                              'text-green-600'
                            }`} />
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900 capitalize">
                              {payment.provider === 'manual' ? 'Manual Payment' :
                               payment.provider === 'stripe' ? 'Stripe Payment' :
                               payment.provider === 'paypal' ? 'PayPal Payment' :
                               payment.provider}
                            </div>
                            <div className="text-xs text-gray-500">{payment.type}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-medium text-gray-900">
                            {formatCurrency(payment.amount, payment.currency_code)}
                          </div>
                          <div className={`text-xs font-medium ${
                            payment.status === 'completed' ? 'text-green-600' :
                            payment.status === 'pending' ? 'text-yellow-600' :
                            'text-red-600'
                          }`}>
                            {payment.status.toUpperCase()}
                          </div>
                        </div>
                      </div>
                      
                      {/* Payment Details */}
                      <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
                        <div>
                          <span className="font-medium">Payment ID:</span>
                          <div className="font-mono text-gray-800">{payment.id}</div>
                        </div>
                        <div>
                          <span className="font-medium">Created:</span>
                          <div>{new Date(payment.created_at).toLocaleString()}</div>
                        </div>
                        {payment.processed_at && (
                          <div>
                            <span className="font-medium">Processed:</span>
                            <div>{new Date(payment.processed_at).toLocaleString()}</div>
                          </div>
                        )}
                        <div>
                          <span className="font-medium">Provider:</span>
                          <div className="font-medium">{payment.provider}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tracking Number Modal */}
      {showTrackingModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                {hasExistingTracking ? 'Update Tracking Information' : 'Add Tracking Information'}
              </h3>
            </div>
            
            <div className="p-6 space-y-4">
              {/* Tracking Number Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tracking Number *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={trackingForm.tracking_number}
                    onChange={(e) => handleTrackingNumberChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter tracking number..."
                  />
                  {isDetectingCourier && (
                    <div className="absolute right-3 top-2.5">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    </div>
                  )}
                </div>
              </div>

              {/* Detected Courier Info */}
              {detectedCourier && (
                <div className={`p-3 rounded-lg border ${
                  detectedCourier.isRecognized 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-yellow-50 border-yellow-200'
                }`}>
                  <div className="flex items-center space-x-2">
                    {detectedCourier.isRecognized ? (
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                    ) : (
                      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
                    )}
                    <div className="flex-1">
                      <div className="text-sm font-medium">
                        {detectedCourier.isRecognized 
                          ? `✅ Detected: ${detectedCourier.courierName}`
                          : '⚠️ Courier not recognized'
                        }
                      </div>
                      {detectedCourier.trackingUrl && (
                        <div className="text-xs text-gray-600 mt-1">
                          Tracking URL: {detectedCourier.trackingUrl}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Manual Courier Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Courier (Optional)
                </label>
                <select
                  value={trackingForm.courier}
                  onChange={(e) => setTrackingForm(prev => ({ ...prev, courier: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Auto-detect or select manually</option>
                  <option value="fan">Fan Courier</option>
                  <option value="cargus">Cargus</option>
                  <option value="dpd">DPD</option>
                  <option value="sameday">Sameday</option>
                  <option value="gls">GLS</option>
                  <option value="other">Other</option>
                </select>
              </div>

              {/* Send Email Option */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="send_email"
                  checked={trackingForm.send_email}
                  onChange={(e) => setTrackingForm(prev => ({ ...prev, send_email: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="send_email" className="text-sm text-gray-700">
                  Send tracking email to customer
                </label>
              </div>

              {trackingForm.send_email && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <EnvelopeIcon className="h-4 w-4 text-blue-500" />
                    <div className="text-sm text-blue-700">
                      Customer will receive an email with tracking information at: <strong>{order.email}</strong>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="px-6 py-4 border-t border-gray-200 flex space-x-3">
              <button
                onClick={handleSubmitTracking}
                disabled={addTrackingMutation.isPending || !trackingForm.tracking_number.trim()}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium disabled:bg-blue-400 disabled:cursor-not-allowed"
              >
                {addTrackingMutation.isPending ? 'Processing...' : 'Save Tracking Info'}
              </button>
              <button
                onClick={() => setShowTrackingModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default OrderDetail; 