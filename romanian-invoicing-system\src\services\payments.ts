import type { Env } from '../index';

export interface PaymentProvider {
  name: string;
  enabled: boolean;
  config: Record<string, any>;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
  client_secret?: string;
  payment_method?: string;
}

export interface PaymentResult {
  success: boolean;
  payment_id?: string;
  error?: string;
  redirect_url?: string;
}

export class PaymentService {
  private env: Env;

  constructor(env: Env) {
    this.env = env;
  }

  // Create payment intent for invoice
  async createPaymentIntent(
    invoiceId: number,
    amount: number,
    currency: string = 'RON',
    provider: string = 'stripe'
  ): Promise<PaymentIntent | null> {
    try {
      switch (provider.toLowerCase()) {
        case 'stripe':
          return await this.createStripePaymentIntent(invoiceId, amount, currency);
        case 'paypal':
          return await this.createPayPalPayment(invoiceId, amount, currency);
        default:
          throw new Error(`Unsupported payment provider: ${provider}`);
      }
    } catch (error) {
      console.error('Payment intent creation error:', error);
      return null;
    }
  }

  // Create Stripe payment intent
  private async createStripePaymentIntent(
    invoiceId: number,
    amount: number,
    currency: string
  ): Promise<PaymentIntent> {
    const stripeSecretKey = await this.env.INVOICING_KV.get('stripe_secret_key');
    if (!stripeSecretKey) {
      throw new Error('Stripe secret key not configured');
    }

    // Convert RON to cents (Stripe expects smallest currency unit)
    const amountInCents = Math.round(amount * 100);

    const response = await fetch('https://api.stripe.com/v1/payment_intents', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${stripeSecretKey}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        amount: amountInCents.toString(),
        currency: currency.toLowerCase(),
        metadata: JSON.stringify({
          invoice_id: invoiceId.toString(),
          source: 'romanian_invoicing_system'
        }),
        automatic_payment_methods: JSON.stringify({ enabled: true })
      })
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Stripe API error: ${error}`);
    }

    const paymentIntent = await response.json();

    return {
      id: paymentIntent.id,
      amount: amount,
      currency: currency,
      status: paymentIntent.status,
      client_secret: paymentIntent.client_secret
    };
  }

  // Create PayPal payment
  private async createPayPalPayment(
    invoiceId: number,
    amount: number,
    currency: string
  ): Promise<PaymentIntent> {
    const paypalClientId = await this.env.INVOICING_KV.get('paypal_client_id');
    const paypalClientSecret = await this.env.INVOICING_KV.get('paypal_client_secret');
    
    if (!paypalClientId || !paypalClientSecret) {
      throw new Error('PayPal credentials not configured');
    }

    // Get PayPal access token
    const tokenResponse = await fetch('https://api.paypal.com/v1/oauth2/token', {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`${paypalClientId}:${paypalClientSecret}`)}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: 'grant_type=client_credentials'
    });

    if (!tokenResponse.ok) {
      throw new Error('Failed to get PayPal access token');
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    // Create PayPal order
    const orderResponse = await fetch('https://api.paypal.com/v2/checkout/orders', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        intent: 'CAPTURE',
        purchase_units: [{
          amount: {
            currency_code: currency,
            value: amount.toFixed(2)
          },
          custom_id: invoiceId.toString()
        }],
        application_context: {
          return_url: `${this.getBaseUrl()}/payment/success`,
          cancel_url: `${this.getBaseUrl()}/payment/cancel`
        }
      })
    });

    if (!orderResponse.ok) {
      const error = await orderResponse.text();
      throw new Error(`PayPal API error: ${error}`);
    }

    const order = await orderResponse.json();

    return {
      id: order.id,
      amount: amount,
      currency: currency,
      status: order.status
    };
  }

  // Process payment webhook
  async processPaymentWebhook(
    provider: string,
    payload: any,
    signature?: string
  ): Promise<boolean> {
    try {
      switch (provider.toLowerCase()) {
        case 'stripe':
          return await this.processStripeWebhook(payload, signature);
        case 'paypal':
          return await this.processPayPalWebhook(payload);
        default:
          console.error(`Unknown payment provider: ${provider}`);
          return false;
      }
    } catch (error) {
      console.error('Payment webhook processing error:', error);
      return false;
    }
  }

  // Process Stripe webhook
  private async processStripeWebhook(payload: any, signature?: string): Promise<boolean> {
    try {
      // Verify webhook signature (in production)
      const webhookSecret = await this.env.INVOICING_KV.get('stripe_webhook_secret');
      if (webhookSecret && signature) {
        // Implement signature verification
        // This is a simplified version - in production, use Stripe's webhook verification
      }

      const event = payload;

      if (event.type === 'payment_intent.succeeded') {
        const paymentIntent = event.data.object;
        const invoiceId = parseInt(paymentIntent.metadata.invoice_id);

        if (invoiceId) {
          await this.markInvoiceAsPaid(invoiceId, {
            payment_method: 'stripe',
            payment_id: paymentIntent.id,
            amount: paymentIntent.amount / 100, // Convert from cents
            currency: paymentIntent.currency.toUpperCase()
          });
        }
      }

      return true;
    } catch (error) {
      console.error('Stripe webhook processing error:', error);
      return false;
    }
  }

  // Process PayPal webhook
  private async processPayPalWebhook(payload: any): Promise<boolean> {
    try {
      const event = payload;

      if (event.event_type === 'CHECKOUT.ORDER.APPROVED') {
        const order = event.resource;
        const invoiceId = parseInt(order.purchase_units[0].custom_id);

        if (invoiceId) {
          await this.markInvoiceAsPaid(invoiceId, {
            payment_method: 'paypal',
            payment_id: order.id,
            amount: parseFloat(order.purchase_units[0].amount.value),
            currency: order.purchase_units[0].amount.currency_code
          });
        }
      }

      return true;
    } catch (error) {
      console.error('PayPal webhook processing error:', error);
      return false;
    }
  }

  // Mark invoice as paid
  private async markInvoiceAsPaid(
    invoiceId: number,
    paymentDetails: {
      payment_method: string;
      payment_id: string;
      amount: number;
      currency: string;
    }
  ): Promise<void> {
    try {
      const currentDate = new Date().toISOString().split('T')[0];

      await this.env.DB.prepare(`
        UPDATE invoices 
        SET 
          paid = 1,
          paid_date = ?,
          paid_amount = ?,
          payment_method = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(
        currentDate,
        paymentDetails.amount,
        paymentDetails.payment_method,
        invoiceId
      ).run();

      // Log payment transaction
      await this.env.DB.prepare(`
        INSERT INTO payment_transactions (
          invoice_id, payment_provider, payment_id, amount, currency, status, transaction_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).bind(
        invoiceId,
        paymentDetails.payment_method,
        paymentDetails.payment_id,
        paymentDetails.amount,
        paymentDetails.currency,
        'completed',
        new Date().toISOString()
      ).run();

      console.log(`Invoice ${invoiceId} marked as paid via ${paymentDetails.payment_method}`);
    } catch (error) {
      console.error('Error marking invoice as paid:', error);
      throw error;
    }
  }

  // Get payment providers configuration
  async getPaymentProviders(companyId: number): Promise<PaymentProvider[]> {
    try {
      const providers: PaymentProvider[] = [];

      // Check Stripe configuration
      const stripeKey = await this.env.INVOICING_KV.get('stripe_secret_key');
      providers.push({
        name: 'stripe',
        enabled: !!stripeKey,
        config: {
          publishable_key: await this.env.INVOICING_KV.get('stripe_publishable_key')
        }
      });

      // Check PayPal configuration
      const paypalClientId = await this.env.INVOICING_KV.get('paypal_client_id');
      providers.push({
        name: 'paypal',
        enabled: !!paypalClientId,
        config: {
          client_id: paypalClientId
        }
      });

      return providers;
    } catch (error) {
      console.error('Error getting payment providers:', error);
      return [];
    }
  }

  // Generate payment link for invoice
  async generatePaymentLink(invoiceId: number): Promise<string | null> {
    try {
      const invoice = await this.env.DB.prepare(`
        SELECT * FROM invoices WHERE id = ?
      `).bind(invoiceId).first();

      if (!invoice || invoice.paid) {
        return null;
      }

      // Generate secure payment token
      const paymentToken = this.generatePaymentToken(invoiceId);
      
      // Store payment token with expiration (24 hours)
      const expirationTime = Date.now() + (24 * 60 * 60 * 1000);
      await this.env.INVOICING_KV.put(
        `payment_token_${paymentToken}`,
        JSON.stringify({ invoice_id: invoiceId, expires: expirationTime }),
        { expirationTtl: 24 * 60 * 60 }
      );

      return `${this.getBaseUrl()}/pay/${paymentToken}`;
    } catch (error) {
      console.error('Error generating payment link:', error);
      return null;
    }
  }

  // Generate secure payment token
  private generatePaymentToken(invoiceId: number): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2);
    return btoa(`${invoiceId}_${timestamp}_${random}`).replace(/[^a-zA-Z0-9]/g, '');
  }

  // Get base URL for payment links
  private getBaseUrl(): string {
    return this.env.ENVIRONMENT === 'production' 
      ? 'https://your-domain.com' 
      : 'https://your-dev-domain.com';
  }
}
