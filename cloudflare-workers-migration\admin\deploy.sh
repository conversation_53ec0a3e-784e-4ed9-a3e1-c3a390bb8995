#!/bin/bash

# HandmadeIn.ro Admin Interface Deployment Script
# Usage: ./deploy.sh [staging|production]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default environment
ENVIRONMENT=${1:-staging}

echo -e "${BLUE}🚀 Deploying HandmadeIn.ro Admin Interface to ${ENVIRONMENT}${NC}"

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    echo -e "${RED}❌ Error: Environment must be 'staging' or 'production'${NC}"
    echo "Usage: $0 [staging|production]"
    exit 1
fi

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    echo -e "${RED}❌ Error: Wrangler CLI is not installed${NC}"
    echo "Install it with: npm install -g wrangler"
    exit 1
fi

# Check if user is logged in
if ! wrangler whoami &> /dev/null; then
    echo -e "${YELLOW}⚠️  You are not logged in to Cloudflare${NC}"
    echo "Please run: wrangler login"
    exit 1
fi

echo -e "${BLUE}📦 Installing dependencies...${NC}"
npm ci

echo -e "${BLUE}🔍 Running type check...${NC}"
npm run type-check

echo -e "${BLUE}🧹 Running linter...${NC}"
npm run lint

echo -e "${BLUE}🏗️  Building for ${ENVIRONMENT}...${NC}"
if [[ "$ENVIRONMENT" == "production" ]]; then
    npm run build:production
else
    npm run build:staging
fi

echo -e "${BLUE}🚀 Deploying to Cloudflare Workers (${ENVIRONMENT})...${NC}"
if [[ "$ENVIRONMENT" == "production" ]]; then
    wrangler deploy --env production
else
    wrangler deploy --env staging
fi

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"

# Get the deployment URL
if [[ "$ENVIRONMENT" == "production" ]]; then
    echo -e "${GREEN}🌐 Production URL: https://handmadein-admin.workers.dev${NC}"
else
    echo -e "${GREEN}🌐 Staging URL: https://staging.handmadein-admin.workers.dev${NC}"
fi

echo -e "${BLUE}📊 You can view deployment status at: https://dash.cloudflare.com/workers${NC}" 