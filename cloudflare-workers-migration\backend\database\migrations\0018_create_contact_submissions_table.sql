-- Migration: 0018_create_contact_submissions_table.sql
-- Description: Create contact submissions table to store contact form submissions

CREATE TABLE contact_submissions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT,
  subject TEXT,
  message TEXT NOT NULL,
  created_at TEXT NOT NULL DEFAULT (datetime('now', 'utc')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now', 'utc')),
  email_id TEXT, -- Resend email ID for tracking
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'replied')),
  replied_at TEXT,
  replied_by TEXT,
  notes TEXT
);

-- <PERSON>reate indexes for performance
CREATE INDEX idx_contact_submissions_email ON contact_submissions(email);
CREATE INDEX idx_contact_submissions_created_at ON contact_submissions(created_at);
CREATE INDEX idx_contact_submissions_status ON contact_submissions(status);

-- <PERSON><PERSON> trigger to update updated_at
CREATE TRIGGER contact_submissions_updated_at 
  AFTER UPDATE ON contact_submissions
  BEGIN
    UPDATE contact_submissions 
    SET updated_at = datetime('now', 'utc') 
    WHERE id = NEW.id;
  END; 