import { Hono } from 'hono';
import type { Env } from '../index';
import { authMiddleware } from '../middleware/auth';
import { z } from 'zod';

const reportRoutes = new Hono<{ Bindings: Env; Variables: { user: any } }>();

// Apply auth middleware to all routes
reportRoutes.use('*', authMiddleware);

// Validation schemas
const dateRangeSchema = z.object({
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  period: z.enum(['today', 'week', 'month', 'quarter', 'year', 'custom']).optional()
});

// Helper function to get date range based on period
function getDateRange(period?: string, startDate?: string, endDate?: string) {
  const now = new Date();
  let start: string, end: string;

  switch (period) {
    case 'today':
      start = end = now.toISOString().split('T')[0];
      break;
    case 'week':
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay());
      start = weekStart.toISOString().split('T')[0];
      end = now.toISOString().split('T')[0];
      break;
    case 'month':
      start = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
      end = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
      break;
    case 'quarter':
      const quarter = Math.floor(now.getMonth() / 3);
      start = new Date(now.getFullYear(), quarter * 3, 1).toISOString().split('T')[0];
      end = new Date(now.getFullYear(), quarter * 3 + 3, 0).toISOString().split('T')[0];
      break;
    case 'year':
      start = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0];
      end = new Date(now.getFullYear(), 11, 31).toISOString().split('T')[0];
      break;
    default:
      start = startDate || new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0];
      end = endDate || now.toISOString().split('T')[0];
  }

  return { start, end };
}

// Comprehensive sales report
reportRoutes.get('/sales', async (c) => {
  try {
    const user = c.get('user');
    const companyId = user.company_id;
    const query = c.req.query();

    const validation = dateRangeSchema.safeParse(query);
    if (!validation.success) {
      return c.json({ success: false, message: 'Parametri invalizi' }, 400);
    }

    const { period, start_date, end_date } = validation.data;
    const { start, end } = getDateRange(period, start_date, end_date);

    // Sales summary
    const salesSummary = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as total_invoices,
        COALESCE(SUM(total), 0) as total_revenue,
        COALESCE(SUM(subtotal), 0) as subtotal,
        COALESCE(SUM(vat_amount), 0) as total_vat,
        COALESCE(SUM(CASE WHEN paid = 1 THEN total ELSE 0 END), 0) as paid_amount,
        COALESCE(SUM(CASE WHEN paid = 0 THEN total ELSE 0 END), 0) as unpaid_amount,
        COUNT(CASE WHEN paid = 1 THEN 1 END) as paid_invoices,
        COUNT(CASE WHEN paid = 0 THEN 1 END) as unpaid_invoices
      FROM invoices
      WHERE company_id = ?
        AND issue_date BETWEEN ? AND ?
    `).bind(companyId, start, end).first();

    // Daily breakdown
    const dailyBreakdown = await c.env.DB.prepare(`
      SELECT
        issue_date,
        COUNT(*) as invoice_count,
        COALESCE(SUM(total), 0) as daily_revenue,
        COALESCE(SUM(CASE WHEN paid = 1 THEN total ELSE 0 END), 0) as paid_revenue
      FROM invoices
      WHERE company_id = ?
        AND issue_date BETWEEN ? AND ?
      GROUP BY issue_date
      ORDER BY issue_date
    `).bind(companyId, start, end).all();

    // Client breakdown
    const clientBreakdown = await c.env.DB.prepare(`
      SELECT
        c.name as client_name,
        c.type as client_type,
        COUNT(i.id) as invoice_count,
        COALESCE(SUM(i.total), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN i.paid = 1 THEN i.total ELSE 0 END), 0) as paid_amount,
        AVG(i.total) as avg_invoice_value
      FROM clients c
      JOIN invoices i ON c.id = i.client_id
      WHERE i.company_id = ?
        AND i.issue_date BETWEEN ? AND ?
      GROUP BY c.id, c.name, c.type
      ORDER BY total_revenue DESC
    `).bind(companyId, start, end).all();

    // Product/Service breakdown
    const productBreakdown = await c.env.DB.prepare(`
      SELECT
        p.name as product_name,
        p.is_service,
        SUM(ii.quantity) as total_quantity,
        COALESCE(SUM(ii.total), 0) as total_revenue,
        COALESCE(SUM(ii.vat_amount), 0) as total_vat,
        COUNT(DISTINCT ii.invoice_id) as invoice_count,
        AVG(ii.unit_price) as avg_unit_price
      FROM products p
      JOIN invoice_items ii ON p.id = ii.product_id
      JOIN invoices i ON ii.invoice_id = i.id
      WHERE i.company_id = ?
        AND i.issue_date BETWEEN ? AND ?
      GROUP BY p.id, p.name, p.is_service
      ORDER BY total_revenue DESC
    `).bind(companyId, start, end).all();

    // VAT breakdown
    const vatBreakdown = await c.env.DB.prepare(`
      SELECT
        ii.vat_rate,
        COALESCE(SUM(ii.subtotal), 0) as taxable_amount,
        COALESCE(SUM(ii.vat_amount), 0) as vat_amount,
        COUNT(DISTINCT ii.invoice_id) as invoice_count
      FROM invoice_items ii
      JOIN invoices i ON ii.invoice_id = i.id
      WHERE i.company_id = ?
        AND i.issue_date BETWEEN ? AND ?
      GROUP BY ii.vat_rate
      ORDER BY ii.vat_rate DESC
    `).bind(companyId, start, end).all();

    return c.json({
      success: true,
      data: {
        period: { start, end },
        summary: salesSummary || {},
        daily_breakdown: dailyBreakdown?.results || [],
        client_breakdown: clientBreakdown?.results || [],
        product_breakdown: productBreakdown?.results || [],
        vat_breakdown: vatBreakdown?.results || []
      }
    });
  } catch (error) {
    console.error('Sales report error:', error);
    return c.json({ success: false, message: 'Eroare la generarea raportului de vânzări' }, 500);
  }
});

// VAT report for ANAF submissions
reportRoutes.get('/vat', async (c) => {
  try {
    const user = c.get('user');
    const companyId = user.company_id;
    const query = c.req.query();

    const validation = dateRangeSchema.safeParse(query);
    if (!validation.success) {
      return c.json({ success: false, message: 'Parametri invalizi' }, 400);
    }

    const { period, start_date, end_date } = validation.data;
    const { start, end } = getDateRange(period, start_date, end_date);

    // VAT summary by rate
    const vatSummary = await c.env.DB.prepare(`
      SELECT
        ii.vat_rate,
        COALESCE(SUM(ii.subtotal), 0) as net_amount,
        COALESCE(SUM(ii.vat_amount), 0) as vat_amount,
        COALESCE(SUM(ii.total), 0) as gross_amount,
        COUNT(DISTINCT i.id) as invoice_count
      FROM invoice_items ii
      JOIN invoices i ON ii.invoice_id = i.id
      WHERE i.company_id = ?
        AND i.issue_date BETWEEN ? AND ?
        AND i.status != 'cancelled'
      GROUP BY ii.vat_rate
      ORDER BY ii.vat_rate DESC
    `).bind(companyId, start, end).all();

    // Monthly VAT breakdown
    const monthlyVat = await c.env.DB.prepare(`
      SELECT
        strftime('%Y-%m', i.issue_date) as month,
        ii.vat_rate,
        COALESCE(SUM(ii.subtotal), 0) as net_amount,
        COALESCE(SUM(ii.vat_amount), 0) as vat_amount
      FROM invoice_items ii
      JOIN invoices i ON ii.invoice_id = i.id
      WHERE i.company_id = ?
        AND i.issue_date BETWEEN ? AND ?
        AND i.status != 'cancelled'
      GROUP BY strftime('%Y-%m', i.issue_date), ii.vat_rate
      ORDER BY month, ii.vat_rate DESC
    `).bind(companyId, start, end).all();

    // Detailed invoice list for VAT
    const invoiceDetails = await c.env.DB.prepare(`
      SELECT
        s.series || i.number as invoice_number,
        i.issue_date,
        c.name as client_name,
        c.cui as client_cui,
        i.subtotal,
        i.vat_amount,
        i.total,
        i.currency
      FROM invoices i
      JOIN clients c ON i.client_id = c.id
      JOIN invoice_series s ON i.series_id = s.id
      WHERE i.company_id = ?
        AND i.issue_date BETWEEN ? AND ?
        AND i.status != 'cancelled'
      ORDER BY i.issue_date, s.series, i.number
    `).bind(companyId, start, end).all();

    return c.json({
      success: true,
      data: {
        period: { start, end },
        vat_summary: vatSummary?.results || [],
        monthly_breakdown: monthlyVat?.results || [],
        invoice_details: invoiceDetails?.results || []
      }
    });
  } catch (error) {
    console.error('VAT report error:', error);
    return c.json({ success: false, message: 'Eroare la generarea raportului TVA' }, 500);
  }
});

// Client analysis report
reportRoutes.get('/clients', async (c) => {
  try {
    const user = c.get('user');
    const companyId = user.company_id;
    const query = c.req.query();

    const validation = dateRangeSchema.safeParse(query);
    if (!validation.success) {
      return c.json({ success: false, message: 'Parametri invalizi' }, 400);
    }

    const { period, start_date, end_date } = validation.data;
    const { start, end } = getDateRange(period, start_date, end_date);

    // Client performance analysis
    const clientAnalysis = await c.env.DB.prepare(`
      SELECT
        c.id,
        c.name,
        c.type,
        c.cui,
        c.city,
        c.county,
        COUNT(i.id) as total_invoices,
        COALESCE(SUM(i.total), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN i.paid = 1 THEN i.total ELSE 0 END), 0) as paid_amount,
        COALESCE(SUM(CASE WHEN i.paid = 0 THEN i.total ELSE 0 END), 0) as outstanding_amount,
        AVG(i.total) as avg_invoice_value,
        MIN(i.issue_date) as first_invoice_date,
        MAX(i.issue_date) as last_invoice_date,
        COUNT(CASE WHEN i.paid = 1 THEN 1 END) as paid_invoices,
        COUNT(CASE WHEN i.paid = 0 THEN 1 END) as unpaid_invoices
      FROM clients c
      LEFT JOIN invoices i ON c.id = i.client_id
        AND i.issue_date BETWEEN ? AND ?
        AND i.status != 'cancelled'
      WHERE c.company_id = ?
      GROUP BY c.id, c.name, c.type, c.cui, c.city, c.county
      ORDER BY total_revenue DESC
    `).bind(start, end, companyId).all();

    // Payment behavior analysis
    const paymentBehavior = await c.env.DB.prepare(`
      SELECT
        c.name as client_name,
        AVG(CASE
          WHEN i.paid = 1 AND i.paid_date IS NOT NULL AND i.due_date IS NOT NULL
          THEN julianday(i.paid_date) - julianday(i.due_date)
          ELSE NULL
        END) as avg_payment_delay,
        COUNT(CASE WHEN i.paid = 1 AND i.due_date < i.paid_date THEN 1 END) as late_payments,
        COUNT(CASE WHEN i.paid = 1 AND i.due_date >= i.paid_date THEN 1 END) as on_time_payments,
        COUNT(CASE WHEN i.paid = 0 AND i.due_date < date('now') THEN 1 END) as overdue_invoices
      FROM clients c
      JOIN invoices i ON c.id = i.client_id
      WHERE i.company_id = ?
        AND i.issue_date BETWEEN ? AND ?
        AND i.status != 'cancelled'
      GROUP BY c.id, c.name
      HAVING COUNT(i.id) > 0
      ORDER BY avg_payment_delay DESC
    `).bind(companyId, start, end).all();

    return c.json({
      success: true,
      data: {
        period: { start, end },
        client_analysis: clientAnalysis?.results || [],
        payment_behavior: paymentBehavior?.results || []
      }
    });
  } catch (error) {
    console.error('Client report error:', error);
    return c.json({ success: false, message: 'Eroare la generarea raportului clienți' }, 500);
  }
});

export { reportRoutes };