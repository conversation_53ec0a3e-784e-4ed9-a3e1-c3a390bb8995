import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  ShoppingBagIcon,
  UsersIcon,
  DocumentTextIcon,
  CogIcon,
  ChartBarIcon,
  PhotoIcon,
  GiftIcon,
  ArchiveBoxIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  MagnifyingGlassIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import clsx from 'clsx';

const navigation = [
  { name: 'Dashboard', href: '/', icon: HomeIcon },
  { 
    name: 'Products', 
    icon: ShoppingBagIcon,
    children: [
      { name: 'All Products', href: '/products' },
      { name: 'Add Product', href: '/products/new' },
      { name: 'Collections', href: '/collections' },
      { name: 'Categories', href: '/categories' },
      { name: 'Product Types', href: '/product-types' },
      { name: 'Tags', href: '/tags' },
    ]
  },
  { 
    name: 'Orders', 
    icon: DocumentTextIcon,
    children: [
      { name: 'All Orders', href: '/orders' },
      { name: 'Draft Orders', href: '/orders/drafts' },
      { name: 'Returns', href: '/returns' },
      { name: 'Exchanges', href: '/exchanges' },
    ]
  },
  { 
    name: 'Customers', 
    icon: UsersIcon,
    children: [
      { name: 'All Customers', href: '/customers' },
      { name: 'Customer Groups', href: '/customer-groups' },
    ]
  },
  { 
    name: 'Inventory', 
    icon: ArchiveBoxIcon,
    children: [
      { name: 'Inventory', href: '/inventory' },
      { name: 'Stock Locations', href: '/inventory/stock-locations' },
      { name: 'Inventory Items', href: '/inventory/items' },
      { name: 'Inventory Levels', href: '/inventory/levels' },
      { name: 'Reservations', href: '/inventory/reservations' },
    ]
  },
  { 
    name: 'Promotions', 
    icon: GiftIcon,
    children: [
      { name: 'Discounts', href: '/discounts' },
      { name: 'Gift Cards', href: '/gift-cards' },
      { name: 'Campaigns', href: '/campaigns' },
    ]
  },
  { 
    name: 'Analytics', 
    icon: ChartBarIcon,
    children: [
      { name: 'Sales', href: '/analytics/sales' },
      { name: 'Products', href: '/analytics/products' },
      { name: 'Customers', href: '/analytics/customers' },
      { name: 'Reports', href: '/analytics/reports' },
    ]
  },
  { name: 'Journal', href: '/journal', icon: DocumentTextIcon },
  { name: 'Files', href: '/files', icon: PhotoIcon },
  { 
    name: 'Settings', 
    icon: CogIcon,
    children: [
      { name: 'General Settings', href: '/settings' },
      { name: 'Store Details', href: '/settings/store' },
      { name: 'Regions', href: '/settings/regions' },
      { name: 'Currencies', href: '/settings/currencies' },
      { name: 'Shipping', href: '/settings/shipping' },
      { name: 'Payment Providers', href: '/settings/payments' },
      { name: 'Tax Settings', href: '/settings/taxes' },
      { name: 'Users', href: '/settings/users' },
      { name: 'API Keys', href: '/settings/api-keys' },
    ]
  },
];

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);
  const [expandedMenus, setExpandedMenus] = React.useState<string[]>([]);
  const location = useLocation();
  const { user, logout } = useAuth();

  const toggleMenu = (menuName: string) => {
    setExpandedMenus(prev => 
      prev.includes(menuName) 
        ? prev.filter(name => name !== menuName)
        : [...prev, menuName]
    );
  };

  const isActiveRoute = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  const hasActiveChild = (children: { href: string }[]) => {
    return children.some(child => isActiveRoute(child.href));
  };

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      {/* Mobile sidebar */}
      <div className={clsx(
        'fixed inset-0 flex z-40 md:hidden',
        sidebarOpen ? 'block' : 'hidden'
      )}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              type="button"
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className="h-6 w-6 text-white" />
            </button>
          </div>
          <SidebarContent 
            navigation={navigation}
            expandedMenus={expandedMenus}
            toggleMenu={toggleMenu}
            isActiveRoute={isActiveRoute}
            hasActiveChild={hasActiveChild}
          />
        </div>
      </div>

      {/* Static sidebar for desktop */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64 h-full">
          <SidebarContent 
            navigation={navigation}
            expandedMenus={expandedMenus}
            toggleMenu={toggleMenu}
            isActiveRoute={isActiveRoute}
            hasActiveChild={hasActiveChild}
          />
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden min-w-0">
        {/* Top navigation */}
        <div className="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
          <button
            type="button"
            className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>
          
          <div className="flex-1 px-4 flex justify-between">
            <div className="flex-1 flex items-center">
              <div className="w-full max-w-lg lg:max-w-xs">
                <label htmlFor="search" className="sr-only">Search</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="search"
                    name="search"
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Search products, orders, customers..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            
            <div className="ml-4 flex items-center md:ml-6">
              <button
                type="button"
                className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <BellIcon className="h-6 w-6" />
              </button>

              <div className="ml-3 relative">
                <div className="flex items-center space-x-3">
                  <UserCircleIcon className="h-8 w-8 text-gray-400" />
                  <div className="text-sm">
                    <div className="font-medium text-gray-700">{user ? `${user.first_name} ${user.last_name}` : 'Admin User'}</div>
                    <div className="text-gray-500">{user?.email || '<EMAIL>'}</div>
                  </div>
                  <button
                    onClick={logout}
                    className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <ArrowRightOnRectangleIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none bg-gray-50">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

interface SidebarContentProps {
  navigation: typeof navigation;
  expandedMenus: string[];
  toggleMenu: (menuName: string) => void;
  isActiveRoute: (href: string) => boolean;
  hasActiveChild: (children: { href: string }[]) => boolean;
}

const SidebarContent: React.FC<SidebarContentProps> = ({
  navigation,
  expandedMenus,
  toggleMenu,
  isActiveRoute,
  hasActiveChild,
}) => {
  return (
    <div className="flex flex-col h-full border-r border-gray-200 bg-white">
      <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
        <div className="flex items-center flex-shrink-0 px-4">
          <img
            className="h-8 w-auto"
            src="/logo.png"
            alt="HandmadeIn.ro"
            onError={(e) => {
              // Hide image if logo doesn't exist
              e.currentTarget.style.display = 'none';
            }}
          />
          <span className="ml-2 text-xl font-bold text-gray-900">Admin</span>
        </div>
        
        <nav className="mt-8 flex-1 px-2 space-y-1">
          {navigation.map((item) => {
            if (item.children) {
              const isExpanded = expandedMenus.includes(item.name);
              const hasActive = hasActiveChild(item.children);
              
              return (
                <div key={item.name}>
                  <button
                    onClick={() => toggleMenu(item.name)}
                    className={clsx(
                      hasActive
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900',
                      'group w-full flex items-center px-2 py-2 text-left text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500'
                    )}
                  >
                    <item.icon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
                    <span className="flex-1">{item.name}</span>
                    <svg
                      className={clsx(
                        isExpanded ? 'text-gray-400 rotate-90' : 'text-gray-300',
                        'ml-3 h-5 w-5 transform group-hover:text-gray-400 transition-colors ease-in-out duration-150'
                      )}
                      viewBox="0 0 20 20"
                      aria-hidden="true"
                    >
                      <path d="M6 6L14 10L6 14V6Z" fill="currentColor" />
                    </svg>
                  </button>
                  
                  {isExpanded && (
                    <div className="space-y-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          to={child.href}
                          className={clsx(
                            isActiveRoute(child.href)
                              ? 'bg-indigo-50 border-indigo-500 text-indigo-700'
                              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900',
                            'group flex items-center pl-11 pr-2 py-2 border-l-4 text-sm font-medium'
                          )}
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              );
            }

            return (
              <Link
                key={item.name}
                to={item.href}
                className={clsx(
                  isActiveRoute(item.href)
                    ? 'bg-gray-100 text-gray-900'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900',
                  'group flex items-center px-2 py-2 text-sm font-medium rounded-md'
                )}
              >
                <item.icon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
                {item.name}
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default Layout; 