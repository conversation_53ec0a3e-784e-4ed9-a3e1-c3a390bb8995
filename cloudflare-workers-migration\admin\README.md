# HandmadeIn.ro Admin Interface

A modern React-based admin interface for HandmadeIn.ro, built with TypeScript, Vite, and deployed on Cloudflare Workers.

## Features

- ⚡ Built with Vite for fast development and optimized builds
- 🔷 TypeScript for type safety
- ⚛️ React 18 with modern hooks
- 🎨 Tailwind CSS for styling
- 📊 React Table for data management
- 🔍 React Query for server state management
- 🛡️ Security headers and best practices

## Prerequisites

- Node.js >= 18.0.0
- npm or yarn
- Cloudflare account with Workers access
- Wrangler CLI installed globally (`npm install -g wrangler`)

## Development

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```
   The app will be available at `http://localhost:3001`

3. **Type checking:**
   ```bash
   npm run type-check
   ```

4. **Linting:**
   ```bash
   npm run lint
   npm run lint:fix
   ```

## Building

### Local Build
```bash
npm run build
```

### Environment-specific Builds
```bash
# Staging build
npm run build:staging

# Production build
npm run build:production
```

## Deployment

### Prerequisites
1. Authenticate with Cloudflare:
   ```bash
   wrangler login
   ```

2. The Worker will be automatically created on first deployment

### Deploy to Staging
```bash
npm run deploy:staging
```

### Deploy to Production
```bash
npm run deploy:production
```

### Quick Deploy (uses default/staging environment)
```bash
npm run deploy
```

### Local Development with Wrangler
Start the development server with Wrangler:
```bash
npm run wrangler:dev
```

## Environment Variables

The application uses different API endpoints for different environments:

### Staging
- `VITE_API_URL`: `https://bapi.handmadein.ro`
- `VITE_APP_ENV`: `staging`

### Production
- `VITE_API_URL`: `https://bapi.handmadein.ro`
- `VITE_APP_ENV`: `production`

These are automatically configured via the wrangler.toml file and build scripts.

## Admin User Setup

To create an admin user for logging into the admin interface, you have several options:

### Option 1: Web-based Signup (Recommended)

The admin interface includes a built-in signup page that's only accessible when no admin users exist:

1. Make sure your backend is running
2. Visit the admin interface at `/signup`
3. Fill out the signup form to create your first admin account
4. You'll be automatically logged in after successful creation

**Note**: The signup page automatically disappears once an admin user exists.

### Option 2: Create Admin User via API Script

1. Make sure your backend is running (locally on port 8787 or deployed)
2. Update the `BACKEND_URL` in the script if needed
3. Run the user creation script:

```bash
node create-admin-user.js
```

This will create an admin user with:
- **Email**: `<EMAIL>`
- **Password**: `Secure@dmin2024!`

### Option 3: Update Existing Admin Password

If the admin user already exists and you need to update the password:

```bash
node update-admin-password.js
```

### Option 4: View Hash Generation Options

To see different password hashing approaches and understand the system:

```bash
node generate-password-hash.js
```

### Security Features:

- ✅ **Auto-disable**: Signup is automatically disabled once an admin user exists
- ✅ **Secure hashing**: Passwords are hashed using scrypt-kdf implementation
- ✅ **Input validation**: Email format, password strength, and required fields validation
- ✅ **Immediate login**: Successful signup automatically logs you in
- ✅ **Smart routing**: Login page shows signup link only when no admin exists

### Important Notes:

- The web signup endpoint is public but automatically disabled after first admin creation
- The development admin key (`admin-dev-key-2024`) is only used for the script-based endpoints
- In production, consider removing the script-based endpoints for enhanced security
- Users are created in the `user` table with authentication in `provider_identity`

## Configuration Files

- **wrangler.toml**: Cloudflare Workers deployment configuration
- **vite.config.ts**: Vite build configuration
- **tailwind.config.js**: Tailwind CSS configuration
- **tsconfig.json**: TypeScript configuration

## Security Features

The deployment includes:
- Security headers (CSP, X-Frame-Options, etc.)
- Static asset caching
- SPA routing support
- HTTPS enforcement

## Project Structure

```
src/
├── components/         # Reusable UI components
├── contexts/          # React contexts
├── lib/              # Utility libraries
├── pages/            # Page components
├── types/            # TypeScript type definitions
├── App.tsx           # Main application component
├── main.tsx          # Application entry point
└── index.css         # Global styles
```

## Troubleshooting

### Build Issues
- Ensure all TypeScript errors are resolved before building
- Check that all dependencies are properly installed
- Verify environment variables are correctly set

### Deployment Issues
- Ensure you're authenticated with Cloudflare (`wrangler login`)
- Check the Workers dashboard for deployment status
- Verify build output exists in `dist/` directory

### Runtime Issues
- Check browser console for JavaScript errors
- Verify API endpoints are accessible
- Ensure environment variables are correctly configured

## Commands Reference

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server |
| `npm run build` | Build for production |
| `npm run build:staging` | Build for staging environment |
| `npm run build:production` | Build for production environment |
| `npm run deploy` | Deploy to staging |
| `npm run deploy:staging` | Deploy to staging environment |
| `npm run deploy:production` | Deploy to production environment |
| `npm run wrangler:dev` | Preview with Wrangler locally |
| `npm run type-check` | Run TypeScript type checking |
| `npm run lint` | Run ESLint |
| `npm run lint:fix` | Fix ESLint issues automatically 