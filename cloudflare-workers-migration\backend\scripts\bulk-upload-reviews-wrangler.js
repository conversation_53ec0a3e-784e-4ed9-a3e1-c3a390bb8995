const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// This script bulk uploads the migrated reviews data using wrangler 
// and your existing REVIEWS_KV binding from wrangler.toml

async function bulkUploadWithWrangler() {
  const kvOperationsFile = path.join(__dirname, '../migration-data/kv-operations.json');
  
  if (!fs.existsSync(kvOperationsFile)) {
    console.error('KV operations file not found. Run migrate:reviews first.');
    process.exit(1);
  }
  
  const kvOperations = JSON.parse(fs.readFileSync(kvOperationsFile, 'utf8'));
  console.log(`Loaded ${kvOperations.length} KV operations for upload`);
  
  // Create a bulk JSON file for wrangler
  const bulkData = kvOperations.map(op => ({
    key: op.key,
    value: op.value
  }));
  
  const bulkFile = path.join(__dirname, '../migration-data/bulk-upload.json');
  fs.writeFileSync(bulkFile, JSON.stringify(bulkData, null, 2));
  
  console.log(`Created bulk upload file: ${bulkFile}`);
  console.log('Uploading to KV using your existing REVIEWS_KV binding...');
  
  try {
    // Use wrangler kv:bulk put with the existing binding (production namespace)
    const command = `wrangler kv:bulk put --binding=REVIEWS_KV --preview false "${bulkFile}"`;
    console.log(`Running: ${command}`);
    
    const output = execSync(command, { 
      encoding: 'utf8',
      cwd: path.join(__dirname, '..')
    });
    
    console.log('Wrangler output:', output);
    console.log(`\n✅ Successfully uploaded all ${kvOperations.length} keys to REVIEWS_KV!`);
    
    // Generate summary
    const summary = {
      timestamp: new Date().toISOString(),
      total_uploaded: kvOperations.length,
      method: 'wrangler bulk upload',
      binding: 'REVIEWS_KV',
      reviews: kvOperations.filter(op => op.type === 'review').length,
      product_reviews: kvOperations.filter(op => op.type === 'product_reviews').length,
      customer_reviews: kvOperations.filter(op => op.type === 'customer_reviews').length,
      product_ratings: kvOperations.filter(op => op.type === 'product_rating').length
    };
    
    const summaryFile = path.join(__dirname, '../migration-data/upload-summary.json');
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
    console.log(`Upload summary written to: ${summaryFile}`);
    
    // Clean up bulk file
    fs.unlinkSync(bulkFile);
    console.log('Temporary bulk file cleaned up.');
    
  } catch (error) {
    console.error('Upload failed:', error.message);
    console.error('\nMake sure you are in the backend directory and wrangler is properly configured.');
    console.error('Also ensure your REVIEWS_KV binding exists in wrangler.toml');
    process.exit(1);
  }
}

bulkUploadWithWrangler().catch(error => {
  console.error('Upload failed:', error);
  process.exit(1);
}); 