-- Romanian Invoicing Software Database Schema
-- Compliant with Romanian fiscal laws and e-Factura requirements

-- Companies/Businesses table
CREATE TABLE companies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    cui TEXT NOT NULL UNIQUE, -- Romanian CIF/CUI (tax identification)
    reg_com TEXT, -- Registrul Comertului (Trade Registry)
    address TEXT NOT NULL,
    city TEXT NOT NULL,
    county TEXT NOT NULL,
    county_code TEXT, -- County code for registration (e.g., "40" for Bucharest)
    postal_code TEXT,
    country TEXT DEFAULT 'România',
    phone TEXT,
    email TEXT,
    bank_account TEXT,
    bank_name TEXT,
    vat_registered BOOLEAN DEFAULT FALSE,
    vat_rate DECIMAL(5,2) DEFAULT 19.00,
    legal_representative TEXT,
    fiscal_representative TEXT,
    authorized_person TEXT,
    logo_path TEXT, -- Path to company logo in R2
    signature_path TEXT, -- Path to digital signature in R2
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Clients table
CREATE TABLE clients (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('individual', 'company')),
    cui TEXT, -- For companies
    cnp TEXT, -- For individuals (CNP - Cod Numeric Personal)
    reg_com TEXT,
    address TEXT NOT NULL,
    city TEXT NOT NULL,
    county TEXT NOT NULL,
    postal_code TEXT,
    country TEXT DEFAULT 'România',
    phone TEXT,
    email TEXT,
    bank_account TEXT,
    bank_name TEXT,
    vat_registered BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    contact_person TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id)
);

-- Products/Services table
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    code TEXT,
    name TEXT NOT NULL,
    description TEXT,
    unit TEXT NOT NULL DEFAULT 'buc', -- Unit of measure (bucăți, kg, m, etc.)
    price DECIMAL(10,2) NOT NULL,
    vat_rate DECIMAL(5,2) NOT NULL CHECK (vat_rate IN (0, 5, 9, 19)), -- Romanian VAT rates
    category TEXT,
    barcode TEXT,
    is_service BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id)
);

-- Invoice series (for sequential numbering)
CREATE TABLE invoice_series (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    series TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('invoice', 'proforma', 'delivery_note', 'receipt')),
    current_number INTEGER NOT NULL DEFAULT 0,
    prefix TEXT,
    suffix TEXT,
    year INTEGER NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id),
    UNIQUE(company_id, series, year)
);

-- Invoices table
CREATE TABLE invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    number TEXT NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE,
    delivery_date DATE,
    currency TEXT DEFAULT 'RON',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    
    -- Amounts
    subtotal DECIMAL(12,4) NOT NULL,
    vat_amount DECIMAL(12,4) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,4) DEFAULT 0,
    total DECIMAL(12,4) NOT NULL,
    
    -- Payment info
    payment_method TEXT,
    payment_terms TEXT,
    paid BOOLEAN DEFAULT FALSE,
    paid_date DATE,
    paid_amount DECIMAL(12,4) DEFAULT 0,
    
    -- Additional fields
    notes TEXT,
    internal_notes TEXT,
    delivery_address TEXT,
    
    -- e-Factura fields
    efactura_id TEXT,
    efactura_status TEXT, -- draft, sent, accepted, rejected
    efactura_sent_at DATETIME,
    efactura_xml TEXT,
    
    -- Legal mentions
    reverse_charge BOOLEAN DEFAULT FALSE,
    vat_exempt BOOLEAN DEFAULT FALSE,
    vat_exempt_reason TEXT,
    
    -- Status
    status TEXT DEFAULT 'draft', -- draft, sent, paid, cancelled, overdue
    cancelled BOOLEAN DEFAULT FALSE,
    cancel_reason TEXT,
    
    -- PDF and XML paths
    pdf_path TEXT,
    xml_path TEXT,
    
    -- ANAF fields
    anaf_upload_index TEXT,
    anaf_status TEXT,
    anaf_message TEXT,
    anaf_uploaded_at DATETIME,
    anaf_validated_xml_path TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id),
    UNIQUE(company_id, series_id, number)
);

-- Invoice items table
CREATE TABLE invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_id INTEGER NOT NULL,
    product_id INTEGER,
    name TEXT NOT NULL,
    description TEXT,
    quantity DECIMAL(10,4) NOT NULL,
    unit TEXT NOT NULL DEFAULT 'buc',
    unit_price DECIMAL(10,4) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,4) DEFAULT 0,
    subtotal DECIMAL(12,4) NOT NULL,
    vat_rate DECIMAL(5,2) NOT NULL,
    vat_amount DECIMAL(12,4) NOT NULL,
    total DECIMAL(12,4) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Proforma invoices (similar structure to invoices but separate)
CREATE TABLE proforma_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    number TEXT NOT NULL,
    issue_date DATE NOT NULL,
    validity_date DATE,
    currency TEXT DEFAULT 'RON',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    
    -- Amounts
    subtotal DECIMAL(12,4) NOT NULL,
    vat_amount DECIMAL(12,4) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,4) DEFAULT 0,
    total DECIMAL(12,4) NOT NULL,
    
    -- Additional fields
    notes TEXT,
    delivery_address TEXT,
    
    -- Status
    status TEXT DEFAULT 'draft',
    converted_to_invoice_id INTEGER,
    
    -- PDF path
    pdf_path TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id),
    FOREIGN KEY (converted_to_invoice_id) REFERENCES invoices(id),
    UNIQUE(company_id, series_id, number)
);

-- Proforma invoice items
CREATE TABLE proforma_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proforma_id INTEGER NOT NULL,
    product_id INTEGER,
    name TEXT NOT NULL,
    description TEXT,
    quantity DECIMAL(10,4) NOT NULL,
    unit TEXT NOT NULL DEFAULT 'buc',
    unit_price DECIMAL(10,4) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,4) DEFAULT 0,
    subtotal DECIMAL(12,4) NOT NULL,
    vat_rate DECIMAL(5,2) NOT NULL,
    vat_amount DECIMAL(12,4) NOT NULL,
    total DECIMAL(12,4) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    
    FOREIGN KEY (proforma_id) REFERENCES proforma_invoices(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Delivery notes (Avize de însoţire)
CREATE TABLE delivery_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    number TEXT NOT NULL,
    issue_date DATE NOT NULL,
    delivery_date DATE,
    vehicle_number TEXT,
    driver_name TEXT,
    transport_type TEXT,
    
    notes TEXT,
    status TEXT DEFAULT 'draft',
    
    -- PDF path
    pdf_path TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id),
    UNIQUE(company_id, series_id, number)
);

-- Delivery note items
CREATE TABLE delivery_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_id INTEGER NOT NULL,
    product_id INTEGER,
    name TEXT NOT NULL,
    quantity DECIMAL(10,4) NOT NULL,
    unit TEXT NOT NULL DEFAULT 'buc',
    notes TEXT,
    sort_order INTEGER DEFAULT 0,
    
    FOREIGN KEY (delivery_id) REFERENCES delivery_notes(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Receipts (Chitanţe)
CREATE TABLE receipts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER,
    invoice_id INTEGER,
    series_id INTEGER NOT NULL,
    number TEXT NOT NULL,
    issue_date DATE NOT NULL,
    amount DECIMAL(12,4) NOT NULL,
    currency TEXT DEFAULT 'RON',
    payment_method TEXT,
    description TEXT,
    received_from TEXT,
    notes TEXT,
    
    -- PDF path
    pdf_path TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id),
    UNIQUE(company_id, series_id, number)
);

-- Settings table
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    key TEXT NOT NULL,
    value TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    UNIQUE(company_id, key)
);

-- Users table for multi-user support
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    email TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    name TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('admin', 'user', 'viewer')),
    is_active BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id)
);

-- Audit log for tracking changes
CREATE TABLE audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    user_id INTEGER,
    table_name TEXT NOT NULL,
    record_id INTEGER NOT NULL,
    action TEXT NOT NULL, -- insert, update, delete
    old_values TEXT, -- JSON
    new_values TEXT, -- JSON
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create indexes for better performance
CREATE INDEX idx_invoices_company_date ON invoices(company_id, issue_date);
CREATE INDEX idx_invoices_client ON invoices(client_id);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoice_items_invoice ON invoice_items(invoice_id);
CREATE INDEX idx_clients_company ON clients(company_id);
CREATE INDEX idx_products_company ON products(company_id);
CREATE INDEX idx_users_company ON users(company_id);
CREATE INDEX idx_audit_company_table ON audit_log(company_id, table_name);

-- Insert default data for first company
INSERT INTO companies (
    id, name, cui, reg_com, address, city, county, county_code,
    phone, email, vat_registered, vat_rate
) VALUES (
    1, 'Exemplu S.R.L.', 'RO12345678', 'J40/123/2024',
    'Strada Exemplu Nr. 1', 'București', 'București', '40',
    '+40123456789', '<EMAIL>', 1, 19.00
);

-- Insert default invoice series
INSERT INTO invoice_series (company_id, series, type, current_number, year, active) VALUES
(1, 'FACT', 'invoice', 0, 2025, 1),
(1, 'PROF', 'proforma', 0, 2025, 1),
(1, 'AVIZ', 'delivery_note', 0, 2025, 1),
(1, 'CHIT', 'receipt', 0, 2025, 1);

-- Recurring invoices table
CREATE TABLE recurring_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    template_name TEXT NOT NULL,
    frequency TEXT NOT NULL CHECK (frequency IN ('weekly', 'monthly', 'quarterly', 'yearly')),
    interval_count INTEGER NOT NULL DEFAULT 1,
    start_date DATE NOT NULL,
    end_date DATE,
    next_generation_date DATE NOT NULL,
    last_generated_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    auto_send_email BOOLEAN DEFAULT FALSE,
    notes TEXT,
    payment_terms TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id)
);

-- Recurring invoice items table
CREATE TABLE recurring_invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    recurring_id INTEGER NOT NULL,
    product_id INTEGER,
    name TEXT NOT NULL,
    description TEXT,
    quantity DECIMAL(10,4) NOT NULL,
    unit TEXT NOT NULL DEFAULT 'buc',
    unit_price DECIMAL(10,4) NOT NULL,
    vat_rate DECIMAL(5,2) NOT NULL,
    sort_order INTEGER DEFAULT 0,

    FOREIGN KEY (recurring_id) REFERENCES recurring_invoices(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Payment reminders table
CREATE TABLE payment_reminders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    invoice_id INTEGER NOT NULL,
    reminder_type TEXT NOT NULL CHECK (reminder_type IN ('gentle', 'firm', 'final')),
    sent_date DATETIME NOT NULL,
    email_sent BOOLEAN DEFAULT FALSE,
    notes TEXT,

    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id)
);

-- Email templates table
CREATE TABLE email_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    template_type TEXT NOT NULL CHECK (template_type IN ('invoice', 'reminder', 'welcome', 'custom')),
    name TEXT NOT NULL,
    subject TEXT NOT NULL,
    html_content TEXT NOT NULL,
    text_content TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (company_id) REFERENCES companies(id)
);

-- Inventory tracking table
CREATE TABLE inventory_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('in', 'out', 'adjustment')),
    quantity DECIMAL(10,4) NOT NULL,
    reference_type TEXT, -- 'invoice', 'manual', 'purchase'
    reference_id INTEGER,
    notes TEXT,
    transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Add recurring_id to invoices table
ALTER TABLE invoices ADD COLUMN recurring_id INTEGER REFERENCES recurring_invoices(id);

-- Add stock tracking to products table
ALTER TABLE products ADD COLUMN track_stock BOOLEAN DEFAULT FALSE;
ALTER TABLE products ADD COLUMN current_stock DECIMAL(10,4) DEFAULT 0;
ALTER TABLE products ADD COLUMN min_stock_level DECIMAL(10,4) DEFAULT 0;

-- Payment transactions table
CREATE TABLE payment_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_id INTEGER NOT NULL,
    payment_provider TEXT NOT NULL,
    payment_id TEXT NOT NULL,
    amount DECIMAL(12,4) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'RON',
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    webhook_data TEXT, -- JSON data from payment provider
    notes TEXT,

    FOREIGN KEY (invoice_id) REFERENCES invoices(id)
);

-- Create additional indexes
CREATE INDEX idx_recurring_invoices_company ON recurring_invoices(company_id);
CREATE INDEX idx_recurring_invoices_next_date ON recurring_invoices(next_generation_date);
CREATE INDEX idx_recurring_items_recurring ON recurring_invoice_items(recurring_id);
CREATE INDEX idx_payment_reminders_invoice ON payment_reminders(invoice_id);
CREATE INDEX idx_email_templates_company ON email_templates(company_id);
CREATE INDEX idx_inventory_transactions_product ON inventory_transactions(product_id);

-- Insert default settings
INSERT INTO settings (company_id, key, value) VALUES
(1, 'vat_rate_standard', '19.00'),
(1, 'vat_rate_reduced_1', '9.00'),
(1, 'vat_rate_reduced_2', '5.00'),
(1, 'currency_default', 'RON'),
(1, 'invoice_due_days', '30'),
(1, 'company_logo_path', ''),
(1, 'company_signature_path', ''),
(1, 'efactura_enabled', 'true'),
(1, 'email_from_address', ''),
(1, 'email_from_name', ''),
(1, 'language', 'ro'),
(1, 'timezone', 'Europe/Bucharest'),
(1, 'anaf_test_mode', 'true'),
(1, 'auto_generate_pdf', 'true'),
(1, 'auto_upload_anaf', 'false'),
(1, 'auto_send_invoices', 'false'),
(1, 'payment_reminder_days', '7,14,30'),
(1, 'auto_payment_reminders', 'false'),
(1, 'inventory_tracking', 'false');