// <PERSON><PERSON>t to update admin user password for HandmadeIn.ro
// This calls the backend API endpoint to properly update the password with the correct hashing

const BACKEND_URL = 'https://bapi.handmadein.ro'; // Change this to your backend URL
const ADMIN_KEY = 'admin-dev-key-2024'; // This should match the key in the backend

const updateData = {
  email: '<EMAIL>',
  password: 'Secure@dmin2024!', // New secure password
  adminKey: ADMIN_KEY
};

console.log('='.repeat(60));
console.log('UPDATING ADMIN USER PASSWORD FOR HANDMADEIN.RO');
console.log('='.repeat(60));
console.log(`Email: ${updateData.email}`);
console.log(`New Password: ${updateData.password}`);
console.log(`Backend URL: ${BACKEND_URL}`);
console.log('');

async function updateAdminPassword() {
  try {
    console.log('Sending request to update admin password...');
    
    const response = await fetch(`${BACKEND_URL}/auth/admin/update-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    });

    const result = await response.json();

    console.log('Response status:', response.status);
    console.log('Response:', result);

    if (response.ok && result.success) {
      console.log('');
      console.log('✅ SUCCESS: Admin password updated successfully!');
      console.log('');
      console.log('Updated login credentials:');
      console.log(`Email: ${updateData.email}`);
      console.log(`Password: ${updateData.password}`);
      console.log('');
      console.log('You can now log in to the admin interface with the new password.');
    } else {
      console.log('');
      console.log('❌ ERROR: Failed to update admin password');
      console.log('Error:', result.error || 'Unknown error');
      
      if (result.error === 'User not found') {
        console.log('');
        console.log('The user does not exist. If you need to create the user, use:');
        console.log('node create-admin-user.js');
      }
    }

  } catch (error) {
    console.error('');
    console.error('❌ REQUEST FAILED:', error.message);
    console.error('');
    console.error('Make sure:');
    console.error('1. The backend is running');
    console.error('2. The backend URL is correct');
    console.error('3. You have internet connectivity');
  }
}

// Main execution
updateAdminPassword().then(() => {
  console.log('');
  console.log('='.repeat(60));
}).catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});

// Export for use in other scripts
module.exports = {
  updateAdminPassword,
  updateData,
  BACKEND_URL
}; 