import { Hono } from 'hono';
import { z } from 'zod';
import type { Env } from '../index';
import { authMiddleware } from '../middleware/auth';
import { XMLParser, XMLBuilder } from 'fast-xml-parser';
import { v4 as uuidv4 } from 'uuid';
import CryptoJ<PERSON> from 'crypto-js';
import puppeteer from '@cloudflare/puppeteer';
import { EmailService } from '../services/email';
import { RecurringInvoiceService } from '../services/recurring';

const invoiceRoutes = new Hono<{ Bindings: Env, Variables: { user: any } }>();

// Validation schemas
const createInvoiceSchema = z.object({
  client_id: z.number(),
  series_id: z.number(),
  issue_date: z.string(),
  due_date: z.string().optional(),
  currency: z.string().default('RON'),
  items: z.array(z.object({
    product_id: z.number().optional(),
    name: z.string(),
    quantity: z.number(),
    unit_price: z.number(),
    vat_rate: z.number()
  })),
  notes: z.string().optional()
});

// PDF Generation Service
class PDFService {
  /**
   * Generates a professional PDF for the given invoice using Cloudflare Browser Rendering (Puppeteer).
   * The method first builds the HTML representation and then renders it to PDF inside a headless
   * Chromium instance. The resulting ArrayBuffer can be streamed directly to the client or stored
   * in R2.
   */
  static async generateInvoicePDF(
    invoice: any,
    company: any,
    client: any,
    items: any[],
    env: Env
  ): Promise<ArrayBuffer> {
    // 1. Build HTML document
    const html = this.generateInvoiceHTML(invoice, company, client, items);

    // 2. Launch headless browser via Cloudflare binding
    const browser = await puppeteer.launch(env.BROWSER);
    const page = await browser.newPage();

    // 3. Load content
    await page.setContent(html, { waitUntil: 'networkidle0' });

    // 4. Render PDF (A4, print backgrounds)
    const pdfBuffer = (await page.pdf({ format: 'A4', printBackground: true })) as ArrayBuffer;

    // 5. Close browser
    await browser.close();

    return pdfBuffer;
  }
  
  static generateInvoiceHTML(invoice: any, company: any, client: any, items: any[]): string {
    return `
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Factură ${invoice.series}${invoice.number}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        
        .invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-name {
            font-size: 20px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        
        .company-details {
            font-size: 11px;
            color: #666;
        }
        
        .invoice-title {
            text-align: right;
            flex: 1;
        }
        
        .invoice-number {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .invoice-date {
            font-size: 12px;
            color: #666;
        }
        
        .parties-section {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
            gap: 40px;
        }
        
        .party-info {
            flex: 1;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
        }
        
        .party-title {
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .party-details {
            font-size: 11px;
            line-height: 1.5;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            font-size: 11px;
        }
        
        .items-table th {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            color: #374151;
        }
        
        .items-table td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            vertical-align: top;
        }
        
        .items-table tr:nth-child(even) {
            background: #f9fafb;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .totals-section {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
        }
        
        .totals-table {
            border-collapse: collapse;
            min-width: 300px;
        }
        
        .totals-table td {
            padding: 8px 15px;
            border: 1px solid #e5e7eb;
        }
        
        .totals-table .label {
            background: #f8fafc;
            font-weight: bold;
            text-align: right;
        }
        
        .totals-table .total-final {
            background: #2563eb;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        
        .legal-info {
            margin-top: 40px;
            padding: 15px;
            background: #f8fafc;
            border-left: 4px solid #2563eb;
            font-size: 10px;
            color: #666;
        }
        
        .signatures {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            text-align: center;
            flex: 1;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            padding-top: 5px;
            font-size: 10px;
        }
        
        @media print {
            body { print-color-adjust: exact; }
            .invoice-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="company-info">
                <div class="company-name">${company.name}</div>
                <div class="company-details">
                    ${company.address}<br>
                    ${company.city}, ${company.county}<br>
                    CUI: ${company.cui}<br>
                    ${company.vat_registered ? `Nr. înregistrare TVA: RO${company.cui}` : 'Neînregistrat TVA'}<br>
                    ${company.phone ? `Tel: ${company.phone}` : ''}<br>
                    ${company.email ? `Email: ${company.email}` : ''}
                </div>
            </div>
            <div class="invoice-title">
                <div class="invoice-number">FACTURĂ ${invoice.series}${invoice.number}</div>
                <div class="invoice-date">Data: ${new Date(invoice.issue_date).toLocaleDateString('ro-RO')}</div>
                ${invoice.due_date ? `<div class="invoice-date">Scadență: ${new Date(invoice.due_date).toLocaleDateString('ro-RO')}</div>` : ''}
            </div>
        </div>
        
        <!-- Parties -->
        <div class="parties-section">
            <div class="party-info">
                <div class="party-title">FURNIZOR</div>
                <div class="party-details">
                    <strong>${company.name}</strong><br>
                    ${company.address}<br>
                    ${company.city}, ${company.county}<br>
                    CUI: ${company.cui}<br>
                    ${company.vat_registered ? `TVA: RO${company.cui}` : 'Neînregistrat TVA'}<br>
                    ${company.reg_com ? `J${company.county_code}/${company.reg_com}` : ''}
                </div>
            </div>
            <div class="party-info">
                <div class="party-title">CLIENT</div>
                <div class="party-details">
                    <strong>${client.name}</strong><br>
                    ${client.address || ''}<br>
                    ${client.city || ''}, ${client.county || ''}<br>
                    ${client.type === 'company' ? `CUI: ${client.cui}` : `CNP: ${client.cnp || ''}`}<br>
                    ${client.vat_registered ? `TVA: RO${client.cui}` : ''}
                </div>
            </div>
        </div>
        
        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%">Nr.</th>
                    <th style="width: 40%">Denumire produs/serviciu</th>
                    <th style="width: 8%">U.M.</th>
                    <th style="width: 10%" class="text-right">Cantitate</th>
                    <th style="width: 12%" class="text-right">Preț unitar (fără TVA)</th>
                    <th style="width: 12%" class="text-right">Valoare (fără TVA)</th>
                    <th style="width: 8%" class="text-center">Cota TVA</th>
                    <th style="width: 12%" class="text-right">TVA</th>
                    <th style="width: 12%" class="text-right">Total</th>
                </tr>
            </thead>
            <tbody>
                ${items.map((item, index) => {
                  const unitPrice = parseFloat(item.unit_price);
                  const quantity = parseFloat(item.quantity);
                  const valueWithoutVat = unitPrice * quantity;
                  const vatRate = parseFloat(item.vat_rate);
                  const vatAmount = valueWithoutVat * (vatRate / 100);
                  const total = valueWithoutVat + vatAmount;
                  
                  return `
                    <tr>
                        <td class="text-center">${index + 1}</td>
                        <td>${item.description}</td>
                        <td class="text-center">${item.unit}</td>
                        <td class="text-right">${quantity.toFixed(2)}</td>
                        <td class="text-right">${unitPrice.toFixed(2)} RON</td>
                        <td class="text-right">${valueWithoutVat.toFixed(2)} RON</td>
                        <td class="text-center">${vatRate.toFixed(0)}%</td>
                        <td class="text-right">${vatAmount.toFixed(2)} RON</td>
                        <td class="text-right">${total.toFixed(2)} RON</td>
                    </tr>
                  `;
                }).join('')}
            </tbody>
        </table>
        
        <!-- Totals -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="label">Total fără TVA:</td>
                    <td class="text-right">${parseFloat(invoice.total_without_vat).toFixed(2)} RON</td>
                </tr>
                <tr>
                    <td class="label">Total TVA:</td>
                    <td class="text-right">${parseFloat(invoice.total_vat).toFixed(2)} RON</td>
                </tr>
                <tr class="total-final">
                    <td class="label">TOTAL DE PLATĂ:</td>
                    <td class="text-right">${parseFloat(invoice.total_amount).toFixed(2)} RON</td>
                </tr>
            </table>
        </div>
        
        <!-- Legal Info -->
        <div class="legal-info">
            <strong>Informații legale:</strong><br>
            Factura se achită în termenul convenit. Întârzierea la plată atrage după sine plata de penalități conform legii.<br>
            ${company.vat_registered ? 'Societatea este plătitoare de TVA conform art. 316 din Legea 227/2015.' : 'Societatea nu este plătitoare de TVA conform art. 310 din Legea 227/2015.'}<br>
            ${invoice.notes ? `Observații: ${invoice.notes}` : ''}
        </div>
        
        <!-- Signatures -->
        <div class="signatures">
            <div class="signature-box">
                <div class="signature-line">Furnizor<br>Nume și semnătură</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">Client<br>Nume și semnătură</div>
            </div>
        </div>
    </div>
</body>
</html>`;
  }
}

// e-Factura Integration Service
class EFacturaService {
  private static readonly ANAF_BASE_URL = 'https://api.anaf.ro/prod/FCTEL/rest';
  private static readonly ANAF_TEST_URL = 'https://api.anaf.ro/test/FCTEL/rest';
  
  static async generateXML(invoice: any, company: any, client: any, items: any[]): Promise<string> {
    const xmlBuilder = new XMLBuilder({
      ignoreAttributes: false,
      format: true,
      suppressEmptyNode: true
    });
    
    // Generate unique invoice ID for e-Factura
    const invoiceId = uuidv4();
    
    const xmlData = {
      Invoice: {
        '@_xmlns': 'urn:oasis:names:specification:ubl:schema:xsd:Invoice-2',
        '@_xmlns:cac': 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2',
        '@_xmlns:cbc': 'urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2',
        
        'cbc:CustomizationID': 'urn:cen.eu:en16931:2017#compliant#urn:efactura.mfinante.ro:CIUS-RO:1.0.1',
        'cbc:ProfileID': 'urn:fdc:peppol.eu:2017:poacc:billing:01:1.0',
        'cbc:ID': `${invoice.series}${invoice.number}`,
        'cbc:IssueDate': new Date(invoice.issue_date).toISOString().split('T')[0],
        'cbc:DueDate': invoice.due_date ? new Date(invoice.due_date).toISOString().split('T')[0] : null,
        'cbc:InvoiceTypeCode': '380', // Commercial invoice
        'cbc:Note': invoice.notes || '',
        'cbc:DocumentCurrencyCode': 'RON',
        'cbc:BuyerReference': client.name,
        
        // Supplier (Company)
        'cac:AccountingSupplierParty': {
          'cac:Party': {
            'cac:PartyName': {
              'cbc:Name': company.name
            },
            'cac:PostalAddress': {
              'cbc:StreetName': company.address,
              'cbc:CityName': company.city,
              'cbc:CountrySubentity': company.county,
              'cbc:Country': {
                'cbc:IdentificationCode': 'RO'
              }
            },
            'cac:PartyTaxScheme': {
              'cbc:CompanyID': company.cui,
              'cac:TaxScheme': {
                'cbc:ID': 'VAT'
              }
            },
            'cac:PartyLegalEntity': {
              'cbc:RegistrationName': company.name,
              'cbc:CompanyID': company.cui
            }
          }
        },
        
        // Customer (Client)
        'cac:AccountingCustomerParty': {
          'cac:Party': {
            'cac:PartyName': {
              'cbc:Name': client.name
            },
            'cac:PostalAddress': {
              'cbc:StreetName': client.address || '',
              'cbc:CityName': client.city || '',
              'cbc:CountrySubentity': client.county || '',
              'cbc:Country': {
                'cbc:IdentificationCode': 'RO'
              }
            },
            'cac:PartyTaxScheme': client.type === 'company' ? {
              'cbc:CompanyID': client.cui,
              'cac:TaxScheme': {
                'cbc:ID': 'VAT'
              }
            } : null,
            'cac:PartyLegalEntity': client.type === 'company' ? {
              'cbc:RegistrationName': client.name,
              'cbc:CompanyID': client.cui
            } : null
          }
        },
        
        // Tax totals
        'cac:TaxTotal': {
          'cbc:TaxAmount': {
            '@_currencyID': 'RON',
            '#text': parseFloat(invoice.total_vat).toFixed(2)
          },
          'cac:TaxSubtotal': this.generateTaxSubtotals(items)
        },
        
        // Monetary totals
        'cac:LegalMonetaryTotal': {
          'cbc:LineExtensionAmount': {
            '@_currencyID': 'RON',
            '#text': parseFloat(invoice.total_without_vat).toFixed(2)
          },
          'cbc:TaxExclusiveAmount': {
            '@_currencyID': 'RON',
            '#text': parseFloat(invoice.total_without_vat).toFixed(2)
          },
          'cbc:TaxInclusiveAmount': {
            '@_currencyID': 'RON',
            '#text': parseFloat(invoice.total_amount).toFixed(2)
          },
          'cbc:PayableAmount': {
            '@_currencyID': 'RON',
            '#text': parseFloat(invoice.total_amount).toFixed(2)
          }
        },
        
        // Invoice lines
        'cac:InvoiceLine': items.map((item, index) => this.generateInvoiceLine(item, index + 1))
      }
    };
    
    return xmlBuilder.build(xmlData);
  }
  
  private static generateTaxSubtotals(items: any[]): any[] {
    const taxGroups = items.reduce((groups, item) => {
      const rate = parseFloat(item.vat_rate);
      if (!groups[rate]) {
        groups[rate] = {
          taxableAmount: 0,
          taxAmount: 0
        };
      }
      const lineTotal = parseFloat(item.unit_price) * parseFloat(item.quantity);
      groups[rate].taxableAmount += lineTotal;
      groups[rate].taxAmount += lineTotal * (rate / 100);
      return groups;
    }, {} as Record<number, { taxableAmount: number; taxAmount: number }>);
    
    return Object.entries(taxGroups).map(([rate, amounts]) => ({
      'cbc:TaxableAmount': {
        '@_currencyID': 'RON',
        '#text': amounts.taxableAmount.toFixed(2)
      },
      'cbc:TaxAmount': {
        '@_currencyID': 'RON',
        '#text': amounts.taxAmount.toFixed(2)
      },
      'cac:TaxCategory': {
        'cbc:ID': 'S',
        'cbc:Percent': parseFloat(rate).toFixed(0),
        'cac:TaxScheme': {
          'cbc:ID': 'VAT'
        }
      }
    }));
  }
  
  private static generateInvoiceLine(item: any, lineNumber: number): any {
    const unitPrice = parseFloat(item.unit_price);
    const quantity = parseFloat(item.quantity);
    const lineTotal = unitPrice * quantity;
    const vatRate = parseFloat(item.vat_rate);
    
    return {
      'cbc:ID': lineNumber.toString(),
      'cbc:InvoicedQuantity': {
        '@_unitCode': item.unit,
        '#text': quantity.toFixed(2)
      },
      'cbc:LineExtensionAmount': {
        '@_currencyID': 'RON',
        '#text': lineTotal.toFixed(2)
      },
      'cac:Item': {
        'cbc:Name': item.description
      },
      'cac:Price': {
        'cbc:PriceAmount': {
          '@_currencyID': 'RON',
          '#text': unitPrice.toFixed(2)
        }
      },
      'cac:TaxTotal': {
        'cbc:TaxAmount': {
          '@_currencyID': 'RON',
          '#text': (lineTotal * (vatRate / 100)).toFixed(2)
        },
        'cac:TaxSubtotal': {
          'cbc:TaxableAmount': {
            '@_currencyID': 'RON',
            '#text': lineTotal.toFixed(2)
          },
          'cbc:TaxAmount': {
            '@_currencyID': 'RON',
            '#text': (lineTotal * (vatRate / 100)).toFixed(2)
          },
          'cac:TaxCategory': {
            'cbc:ID': 'S',
            'cbc:Percent': vatRate.toFixed(0),
            'cac:TaxScheme': {
              'cbc:ID': 'VAT'
            }
          }
        }
      }
    };
  }
  
  static async uploadToANAF(xmlContent: string, accessToken: string, isTest: boolean = false): Promise<any> {
    const baseUrl = isTest ? this.ANAF_TEST_URL : this.ANAF_BASE_URL;
    const uploadUrl = `${baseUrl}/upload`;
    
    const formData = new FormData();
    formData.append('file', new Blob([xmlContent], { type: 'application/xml' }), 'invoice.xml');
    
    const response = await fetch(uploadUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json'
      },
      body: formData
    });
    
    if (!response.ok) {
      throw new Error(`ANAF upload failed: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  static async downloadFromANAF(uploadIndex: string, accessToken: string, isTest: boolean = false): Promise<any> {
    const baseUrl = isTest ? this.ANAF_TEST_URL : this.ANAF_BASE_URL;
    const downloadUrl = `${baseUrl}/download?id=${uploadIndex}`;
    
    const response = await fetch(downloadUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/xml'
      }
    });
    
    if (!response.ok) {
      throw new Error(`ANAF download failed: ${response.status} ${response.statusText}`);
    }
    
    return await response.text();
  }
  
  static async getUploadStatus(uploadIndex: string, accessToken: string, isTest: boolean = false): Promise<any> {
    const baseUrl = isTest ? this.ANAF_TEST_URL : this.ANAF_BASE_URL;
    const statusUrl = `${baseUrl}/status?id=${uploadIndex}`;
    
    const response = await fetch(statusUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`ANAF status check failed: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  static async listReceivedInvoices(cui: string, accessToken: string, days: number = 60, isTest: boolean = false): Promise<any> {
    const baseUrl = isTest ? this.ANAF_TEST_URL : this.ANAF_BASE_URL;
    const listUrl = `${baseUrl}/list?cui=${cui}&days=${days}`;
    
    const response = await fetch(listUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`ANAF list failed: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }
}

// Apply auth middleware
invoiceRoutes.use('*', authMiddleware);

// Get all invoices for company
invoiceRoutes.get('/', async (c) => {
  try {
    const user = c.get('user');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;

    const invoices = await c.env.DB.prepare(`
      SELECT 
        i.*,
        c.name as client_name,
        c.cui as client_cui,
        s.series,
        s.prefix
      FROM invoices i
      JOIN clients c ON i.client_id = c.id
      JOIN invoice_series s ON i.series_id = s.id
      WHERE i.company_id = ?
      ORDER BY i.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(user.company_id, limit, offset).all();

    const totalResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as total FROM invoices WHERE company_id = ?
    `).bind(user.company_id).first();

    return c.json({
      success: true,
      data: {
        invoices: invoices.results,
        pagination: {
          page,
          limit,
          total: totalResult?.total || 0,
          totalPages: Math.ceil((totalResult?.total || 0) / limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching invoices:', error);
    return c.json({ 
      success: false, 
      message: 'Eroare la încărcarea facturilor' 
    }, 500);
  }
});

// Get single invoice
invoiceRoutes.get('/:id(\\d+)', async (c) => {
  try {
    const user = c.get('user');
    const invoiceId = parseInt(c.req.param('id'));

    const invoice = await c.env.DB.prepare(`
      SELECT 
        i.*,
        c.name as client_name,
        c.cui as client_cui,
        c.address as client_address,
        c.city as client_city,
        c.county as client_county,
        s.series,
        s.prefix
      FROM invoices i
      JOIN clients c ON i.client_id = c.id
      JOIN invoice_series s ON i.series_id = s.id
      WHERE i.id = ? AND i.company_id = ?
    `).bind(invoiceId, user.company_id).first();

    if (!invoice) {
      return c.json({ 
        success: false, 
        message: 'Factura nu a fost găsită' 
      }, 404);
    }

    // Get invoice items
    const items = await c.env.DB.prepare(`
      SELECT * FROM invoice_items WHERE invoice_id = ? ORDER BY sort_order
    `).bind(invoiceId).all();

    return c.json({
      success: true,
      data: {
        invoice,
        items: items.results
      }
    });

  } catch (error) {
    console.error('Error fetching invoice:', error);
    return c.json({ 
      success: false, 
      message: 'Eroare la încărcarea facturii' 
    }, 500);
  }
});

// Create new invoice
invoiceRoutes.post('/', async (c) => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    const validation = createInvoiceSchema.safeParse(body);
    
    if (!validation.success) {
      return c.json({ 
        success: false, 
        message: 'Date invalide',
        errors: validation.error.errors 
      }, 400);
    }

    const data = validation.data;

    // Get next invoice number
    const series = await c.env.DB.prepare(`
      SELECT * FROM invoice_series WHERE id = ? AND company_id = ? AND active = 1
    `).bind(data.series_id, user.company_id).first();

    if (!series) {
      return c.json({ 
        success: false, 
        message: 'Seria de facturi nu este validă' 
      }, 400);
    }

    const nextNumber = (series.current_number || 0) + 1;
    const invoiceNumber = `${series.prefix}${series.year}-${nextNumber.toString().padStart(3, '0')}`;

    // Calculate totals
    let subtotal = 0;
    let vatAmount = 0;
    
    for (const item of data.items) {
      const itemSubtotal = item.quantity * item.unit_price;
      const itemVat = itemSubtotal * (item.vat_rate / 100);
      subtotal += itemSubtotal;
      vatAmount += itemVat;
    }

    const total = subtotal + vatAmount;

    // Create invoice
    const invoiceResult = await c.env.DB.prepare(`
      INSERT INTO invoices (
        company_id, client_id, series_id, number, issue_date, due_date,
        currency, subtotal, vat_amount, total, notes, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft')
    `).bind(
      user.company_id,
      data.client_id,
      data.series_id,
      invoiceNumber,
      data.issue_date,
      data.due_date || null,
      data.currency,
      subtotal,
      vatAmount,
      total,
      data.notes || null
    ).run();

    if (!invoiceResult.success) {
      return c.json({ 
        success: false, 
        message: 'Eroare la crearea facturii' 
      }, 500);
    }

    const invoiceId = invoiceResult.meta.last_row_id;

    // Create invoice items
    for (let i = 0; i < data.items.length; i++) {
      const item = data.items[i];
      const itemSubtotal = item.quantity * item.unit_price;
      const itemVat = itemSubtotal * (item.vat_rate / 100);
      const itemTotal = itemSubtotal + itemVat;

      await c.env.DB.prepare(`
        INSERT INTO invoice_items (
          invoice_id, product_id, name, quantity, unit_price, 
          subtotal, vat_rate, vat_amount, total, sort_order
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        invoiceId,
        item.product_id || null,
        item.name,
        item.quantity,
        item.unit_price,
        itemSubtotal,
        item.vat_rate,
        itemVat,
        itemTotal,
        i
      ).run();
    }

    // Update series current number
    await c.env.DB.prepare(`
      UPDATE invoice_series SET current_number = ? WHERE id = ?
    `).bind(nextNumber, data.series_id).run();

    return c.json({
      success: true,
      message: 'Factura a fost creată cu succes',
      data: { invoice_id: invoiceId, number: invoiceNumber }
    });

  } catch (error) {
    console.error('Error creating invoice:', error);
    return c.json({ 
      success: false, 
      message: 'Eroare la crearea facturii' 
    }, 500);
  }
});

// Update invoice
invoiceRoutes.put('/:id', async (c) => {
  try {
    const user = c.get('user');
    const invoiceId = parseInt(c.req.param('id'));
    const body = await c.req.json();

    // Check if invoice exists and belongs to company
    const existingInvoice = await c.env.DB.prepare(`
      SELECT * FROM invoices WHERE id = ? AND company_id = ?
    `).bind(invoiceId, user.company_id).first();

    if (!existingInvoice) {
      return c.json({ 
        success: false, 
        message: 'Factura nu a fost găsită' 
      }, 404);
    }

    if (existingInvoice.status !== 'draft') {
      return c.json({ 
        success: false, 
        message: 'Doar facturile în stadiul draft pot fi modificate' 
      }, 400);
    }

    // Update invoice (simplified - in production would need full validation)
    await c.env.DB.prepare(`
      UPDATE invoices SET 
        notes = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(body.notes || null, invoiceId).run();

    return c.json({
      success: true,
      message: 'Factura a fost actualizată cu succes'
    });

  } catch (error) {
    console.error('Error updating invoice:', error);
    return c.json({ 
      success: false, 
      message: 'Eroare la actualizarea facturii' 
    }, 500);
  }
});

// Delete invoice
invoiceRoutes.delete('/:id', async (c) => {
  try {
    const user = c.get('user');
    const invoiceId = parseInt(c.req.param('id'));

    // Check if invoice exists and can be deleted
    const invoice = await c.env.DB.prepare(`
      SELECT * FROM invoices WHERE id = ? AND company_id = ?
    `).bind(invoiceId, user.company_id).first();

    if (!invoice) {
      return c.json({ 
        success: false, 
        message: 'Factura nu a fost găsită' 
      }, 404);
    }

    if (invoice.status !== 'draft') {
      return c.json({ 
        success: false, 
        message: 'Doar facturile în stadiul draft pot fi șterse' 
      }, 400);
    }

    // Delete invoice items first
    await c.env.DB.prepare(`
      DELETE FROM invoice_items WHERE invoice_id = ?
    `).bind(invoiceId).run();

    // Delete invoice
    await c.env.DB.prepare(`
      DELETE FROM invoices WHERE id = ?
    `).bind(invoiceId).run();

    return c.json({
      success: true,
      message: 'Factura a fost ștearsă cu succes'
    });

  } catch (error) {
    console.error('Error deleting invoice:', error);
    return c.json({ 
      success: false, 
      message: 'Eroare la ștergerea facturii' 
    }, 500);
  }
});

// Generate PDF for invoice
invoiceRoutes.get('/:id(\\d+)/pdf', async (c) => {
  try {
    const { id } = c.req.param();
    const { companyId } = c.get('user');
    
    // Get invoice with related data
    const invoiceStmt = c.env.DB.prepare(`
      SELECT i.*, c.name as client_name, c.type as client_type, c.cui as client_cui, 
             c.cnp as client_cnp, c.address as client_address, c.city as client_city, 
             c.county as client_county, c.vat_registered as client_vat_registered
      FROM invoices i
      JOIN clients c ON i.client_id = c.id
      WHERE i.id = ? AND i.company_id = ?
    `);
    
    const invoice = await invoiceStmt.bind(id, companyId).first();
    if (!invoice) {
      return c.json({ error: 'Factura nu a fost găsită' }, 404);
    }
    
    // Get company info
    const company = await c.env.DB.prepare('SELECT * FROM companies WHERE id = ?').bind(companyId).first();
    
    // Get invoice items
    const items = await c.env.DB.prepare('SELECT * FROM invoice_items WHERE invoice_id = ?').bind(id).all();
    
    // Generate PDF
    const pdfBuffer = await PDFService.generateInvoicePDF(
      invoice,
      company,
      {
        name: invoice.client_name,
        type: invoice.client_type,
        cui: invoice.client_cui,
        cnp: invoice.client_cnp,
        address: invoice.client_address,
        city: invoice.client_city,
        county: invoice.client_county,
        vat_registered: invoice.client_vat_registered
      },
      items.results || [],
      c.env
    );
    
    // Store PDF in R2 for future retrieval
    const pdfKey = `invoices/${companyId}/${id}/invoice-${invoice.series}${invoice.number}.pdf`;
    await c.env.INVOICING_FILES.put(pdfKey, pdfBuffer, {
      httpMetadata: {
        contentType: 'application/pdf',
        contentDisposition: `attachment; filename="Factura-${invoice.series}${invoice.number}.pdf"`
      }
    });

    // Update invoice record with pdf_path
    await c.env.DB.prepare('UPDATE invoices SET pdf_path = ? WHERE id = ?').bind(pdfKey, id).run();

    return new Response(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="Factura-${invoice.series}${invoice.number}.pdf"`
      }
    });
    
  } catch (error) {
    console.error('PDF generation error:', error);
    return c.json({ error: 'Eroare la generarea PDF-ului' }, 500);
  }
});

// Generate e-Factura XML
invoiceRoutes.get('/:id(\\d+)/xml', async (c) => {
  try {
    const { id } = c.req.param();
    const { companyId } = c.get('user');
    
    // Get invoice with related data
    const invoiceStmt = c.env.DB.prepare(`
      SELECT i.*, c.name as client_name, c.type as client_type, c.cui as client_cui, 
             c.cnp as client_cnp, c.address as client_address, c.city as client_city, 
             c.county as client_county, c.vat_registered as client_vat_registered
      FROM invoices i
      JOIN clients c ON i.client_id = c.id
      WHERE i.id = ? AND i.company_id = ?
    `);
    
    const invoice = await invoiceStmt.bind(id, companyId).first();
    if (!invoice) {
      return c.json({ error: 'Factura nu a fost găsită' }, 404);
    }
    
    // Get company info
    const company = await c.env.DB.prepare('SELECT * FROM companies WHERE id = ?').bind(companyId).first();
    
    // Get invoice items
    const items = await c.env.DB.prepare('SELECT * FROM invoice_items WHERE invoice_id = ?').bind(id).all();
    
    // Generate XML
    const xmlContent = await EFacturaService.generateXML(
      invoice,
      company,
      {
        name: invoice.client_name,
        type: invoice.client_type,
        cui: invoice.client_cui,
        cnp: invoice.client_cnp,
        address: invoice.client_address,
        city: invoice.client_city,
        county: invoice.client_county,
        vat_registered: invoice.client_vat_registered
      },
      items.results || []
    );
    
    // Store XML in R2
    const xmlKey = `invoices/${companyId}/${id}/invoice-${invoice.series}${invoice.number}.xml`;
    await c.env.INVOICING_FILES.put(xmlKey, xmlContent, {
      httpMetadata: {
        contentType: 'application/xml',
        contentDisposition: `attachment; filename="Factura-${invoice.series}${invoice.number}.xml"`
      }
    });
    
    // Update invoice with XML path
    await c.env.DB.prepare('UPDATE invoices SET xml_path = ? WHERE id = ?').bind(xmlKey, id).run();
    
    return new Response(xmlContent, {
      headers: {
        'Content-Type': 'application/xml',
        'Content-Disposition': `attachment; filename="Factura-${invoice.series}${invoice.number}.xml"`
      }
    });
    
  } catch (error) {
    console.error('XML generation error:', error);
    return c.json({ error: 'Eroare la generarea XML-ului' }, 500);
  }
});

// Upload invoice to ANAF e-Factura
invoiceRoutes.post('/:id/upload-anaf', async (c) => {
  try {
    const { id } = c.req.param();
    const { companyId } = c.get('user');
    const { isTest = true } = await c.req.json();
    
    // Get invoice
    const invoice = await c.env.DB.prepare('SELECT * FROM invoices WHERE id = ? AND company_id = ?').bind(id, companyId).first();
    if (!invoice) {
      return c.json({ error: 'Factura nu a fost găsită' }, 404);
    }
    
    // Check if XML exists
    if (!invoice.xml_path) {
      return c.json({ error: 'XML-ul facturii nu a fost generat' }, 400);
    }
    
    // Get XML content from R2
    const xmlObject = await c.env.INVOICING_FILES.get(invoice.xml_path);
    if (!xmlObject) {
      return c.json({ error: 'Fișierul XML nu a fost găsit' }, 404);
    }
    
    const xmlContent = await xmlObject.text();
    
    // Get ANAF access token from KV or settings
    const accessToken = await c.env.INVOICING_KV.get(`anaf_token_${companyId}`);
    if (!accessToken) {
      return c.json({ error: 'Token-ul ANAF nu este configurat' }, 400);
    }
    
    // Upload to ANAF
    const uploadResult = await EFacturaService.uploadToANAF(xmlContent, accessToken, isTest);
    
    // Update invoice with ANAF upload index
    await c.env.DB.prepare(`
      UPDATE invoices 
      SET anaf_upload_index = ?, anaf_status = 'uploaded', anaf_uploaded_at = datetime('now')
      WHERE id = ?
    `).bind(uploadResult.upload_index || uploadResult.index, id).run();
    
    return c.json({
      success: true,
      uploadIndex: uploadResult.upload_index || uploadResult.index,
      message: 'Factura a fost încărcată cu succes în sistemul ANAF e-Factura'
    });
    
  } catch (error) {
    console.error('ANAF upload error:', error);
    return c.json({ 
      error: 'Eroare la încărcarea în ANAF',
      details: error.message 
    }, 500);
  }
});

// Check ANAF upload status
invoiceRoutes.get('/:id(\\d+)/anaf-status', async (c) => {
  try {
    const { id } = c.req.param();
    const { companyId } = c.get('user');
    
    // Get invoice
    const invoice = await c.env.DB.prepare('SELECT * FROM invoices WHERE id = ? AND company_id = ?').bind(id, companyId).first();
    if (!invoice) {
      return c.json({ error: 'Factura nu a fost găsită' }, 404);
    }
    
    if (!invoice.anaf_upload_index) {
      return c.json({ error: 'Factura nu a fost încărcată în ANAF' }, 400);
    }
    
    // Get ANAF access token
    const accessToken = await c.env.INVOICING_KV.get(`anaf_token_${companyId}`);
    if (!accessToken) {
      return c.json({ error: 'Token-ul ANAF nu este configurat' }, 400);
    }
    
    // Check status
    const status = await EFacturaService.getUploadStatus(invoice.anaf_upload_index, accessToken, true);
    
    // Update invoice status
    await c.env.DB.prepare(`
      UPDATE invoices 
      SET anaf_status = ?, anaf_message = ?
      WHERE id = ?
    `).bind(status.status || 'unknown', status.message || '', id).run();
    
    return c.json({
      uploadIndex: invoice.anaf_upload_index,
      status: status.status,
      message: status.message,
      details: status
    });
    
  } catch (error) {
    console.error('ANAF status check error:', error);
    return c.json({ 
      error: 'Eroare la verificarea statusului ANAF',
      details: error.message 
    }, 500);
  }
});

// Download validated invoice from ANAF
invoiceRoutes.get('/:id(\\d+)/download-anaf', async (c) => {
  try {
    const { id } = c.req.param();
    const { companyId } = c.get('user');
    
    // Get invoice
    const invoice = await c.env.DB.prepare('SELECT * FROM invoices WHERE id = ? AND company_id = ?').bind(id, companyId).first();
    if (!invoice) {
      return c.json({ error: 'Factura nu a fost găsită' }, 404);
    }
    
    if (!invoice.anaf_upload_index) {
      return c.json({ error: 'Factura nu a fost încărcată în ANAF' }, 400);
    }
    
    // Get ANAF access token
    const accessToken = await c.env.INVOICING_KV.get(`anaf_token_${companyId}`);
    if (!accessToken) {
      return c.json({ error: 'Token-ul ANAF nu este configurat' }, 400);
    }
    
    // Download validated XML
    const validatedXml = await EFacturaService.downloadFromANAF(invoice.anaf_upload_index, accessToken, true);
    
    // Store validated XML in R2
    const validatedXmlKey = `invoices/${companyId}/${id}/invoice-${invoice.series}${invoice.number}-validated.xml`;
    await c.env.INVOICING_FILES.put(validatedXmlKey, validatedXml, {
      httpMetadata: {
        contentType: 'application/xml',
        contentDisposition: `attachment; filename="Factura-${invoice.series}${invoice.number}-validated.xml"`
      }
    });
    
    // Update invoice
    await c.env.DB.prepare(`
      UPDATE invoices 
      SET anaf_validated_xml_path = ?, anaf_status = 'validated'
      WHERE id = ?
    `).bind(validatedXmlKey, id).run();
    
    return new Response(validatedXml, {
      headers: {
        'Content-Type': 'application/xml',
        'Content-Disposition': `attachment; filename="Factura-${invoice.series}${invoice.number}-validated.xml"`
      }
    });
    
  } catch (error) {
    console.error('ANAF download error:', error);
    return c.json({ 
      error: 'Eroare la descărcarea din ANAF',
      details: error.message 
    }, 500);
  }
});

// List received invoices from ANAF
invoiceRoutes.get('/anaf/received', async (c) => {
  try {
    const { companyId } = c.get('user');
    const { days = '60' } = c.req.query();
    
    // Get company CUI
    const company = await c.env.DB.prepare('SELECT cui FROM companies WHERE id = ?').bind(companyId).first();
    if (!company) {
      return c.json({ error: 'Compania nu a fost găsită' }, 404);
    }
    
    // Get ANAF access token
    const accessToken = await c.env.INVOICING_KV.get(`anaf_token_${companyId}`);
    if (!accessToken) {
      return c.json({ error: 'Token-ul ANAF nu este configurat' }, 400);
    }
    
    // Get received invoices list
    const receivedInvoices = await EFacturaService.listReceivedInvoices(company.cui, accessToken, parseInt(days), true);
    
    return c.json({
      success: true,
      invoices: receivedInvoices.invoices || [],
      count: receivedInvoices.count || 0
    });
    
  } catch (error) {
    console.error('ANAF received invoices error:', error);
    return c.json({
      error: 'Eroare la obținerea facturilor primite din ANAF',
      details: error.message
    }, 500);
  }
});

// Recurring Invoices Routes

// Get all recurring invoices
invoiceRoutes.get('/recurring', async (c) => {
  try {
    const user = c.get('user');
    if (!user || !user.company_id) {
      return c.json({ success: false, message: 'User not authenticated' }, 401);
    }

    const companyId = user.company_id;
    console.log('Getting recurring invoices for company:', companyId);

    const recurringService = new RecurringInvoiceService(c.env);
    const recurringInvoices = await recurringService.getRecurringInvoices(companyId);

    console.log('Found recurring invoices:', recurringInvoices.length);

    return c.json({
      success: true,
      data: recurringInvoices
    });
  } catch (error) {
    console.error('Get recurring invoices error:', error);
    return c.json({ success: false, message: 'Eroare la încărcarea facturilor recurente: ' + error.message }, 500);
  }
});

// Create recurring invoice
invoiceRoutes.post('/recurring', async (c) => {
  try {
    const user = c.get('user');
    const companyId = user.company_id;
    const body = await c.req.json();

    const recurringSchema = z.object({
      client_id: z.number(),
      series_id: z.number(),
      template_name: z.string(),
      frequency: z.enum(['weekly', 'monthly', 'quarterly', 'yearly']),
      interval_count: z.number().min(1).default(1),
      start_date: z.string(),
      end_date: z.string().optional(),
      auto_send_email: z.boolean().default(false),
      items: z.array(z.object({
        product_id: z.number().optional(),
        name: z.string(),
        description: z.string().optional(),
        quantity: z.number(),
        unit: z.string().default('buc'),
        unit_price: z.number(),
        vat_rate: z.number()
      })),
      notes: z.string().optional(),
      payment_terms: z.string().optional()
    });

    const validation = recurringSchema.safeParse(body);
    if (!validation.success) {
      return c.json({ success: false, message: 'Date invalide', errors: validation.error.errors }, 400);
    }

    const data = validation.data;

    // Calculate next generation date
    const startDate = new Date(data.start_date);
    const nextGenerationDate = startDate.toISOString().split('T')[0];

    const recurringService = new RecurringInvoiceService(c.env);
    const recurringId = await recurringService.createRecurringInvoice({
      company_id: companyId,
      client_id: data.client_id,
      series_id: data.series_id,
      template_name: data.template_name,
      frequency: data.frequency,
      interval_count: data.interval_count,
      start_date: data.start_date,
      end_date: data.end_date,
      next_generation_date: nextGenerationDate,
      is_active: true,
      auto_send_email: data.auto_send_email,
      items: data.items,
      notes: data.notes,
      payment_terms: data.payment_terms
    });

    return c.json({
      success: true,
      data: { id: recurringId },
      message: 'Factura recurentă a fost creată cu succes'
    });
  } catch (error) {
    console.error('Create recurring invoice error:', error);
    return c.json({ success: false, message: 'Eroare la crearea facturii recurente' }, 500);
  }
});

// Update recurring invoice
invoiceRoutes.put('/recurring/:id', async (c) => {
  try {
    const user = c.get('user');
    const recurringId = parseInt(c.req.param('id'));
    const body = await c.req.json();

    const updateSchema = z.object({
      template_name: z.string().optional(),
      frequency: z.enum(['weekly', 'monthly', 'quarterly', 'yearly']).optional(),
      interval_count: z.number().min(1).optional(),
      is_active: z.boolean().optional(),
      auto_send_email: z.boolean().optional(),
      end_date: z.string().optional()
    });

    const validation = updateSchema.safeParse(body);
    if (!validation.success) {
      return c.json({ success: false, message: 'Date invalide', errors: validation.error.errors }, 400);
    }

    const recurringService = new RecurringInvoiceService(c.env);
    const success = await recurringService.updateRecurringInvoice(recurringId, validation.data);

    if (success) {
      return c.json({
        success: true,
        message: 'Factura recurentă a fost actualizată cu succes'
      });
    } else {
      return c.json({ success: false, message: 'Eroare la actualizarea facturii recurente' }, 500);
    }
  } catch (error) {
    console.error('Update recurring invoice error:', error);
    return c.json({ success: false, message: 'Eroare la actualizarea facturii recurente' }, 500);
  }
});

// Delete recurring invoice
invoiceRoutes.delete('/recurring/:id', async (c) => {
  try {
    const user = c.get('user');
    const recurringId = parseInt(c.req.param('id'));

    const recurringService = new RecurringInvoiceService(c.env);
    const success = await recurringService.deleteRecurringInvoice(recurringId);

    if (success) {
      return c.json({
        success: true,
        message: 'Factura recurentă a fost ștearsă cu succes'
      });
    } else {
      return c.json({ success: false, message: 'Eroare la ștergerea facturii recurente' }, 500);
    }
  } catch (error) {
    console.error('Delete recurring invoice error:', error);
    return c.json({ success: false, message: 'Eroare la ștergerea facturii recurente' }, 500);
  }
});

// Process due recurring invoices (can be called by cron job)
invoiceRoutes.post('/recurring/process', async (c) => {
  try {
    const recurringService = new RecurringInvoiceService(c.env);
    await recurringService.processDueRecurringInvoices();

    return c.json({
      success: true,
      message: 'Facturile recurente au fost procesate cu succes'
    });
  } catch (error) {
    console.error('Process recurring invoices error:', error);
    return c.json({ success: false, message: 'Eroare la procesarea facturilor recurente' }, 500);
  }
});

// Test route to check database tables
invoiceRoutes.get('/test-db', async (c) => {
  try {
    // Test if recurring_invoices table exists
    const testQuery = await c.env.DB.prepare(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='recurring_invoices'
    `).first();

    const tablesQuery = await c.env.DB.prepare(`
      SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
    `).all();

    return c.json({
      success: true,
      data: {
        recurring_table_exists: !!testQuery,
        all_tables: tablesQuery.results?.map(t => t.name) || []
      }
    });
  } catch (error) {
    console.error('Database test error:', error);
    return c.json({ success: false, message: 'Database test failed: ' + error.message }, 500);
  }
});

export { invoiceRoutes as invoices };