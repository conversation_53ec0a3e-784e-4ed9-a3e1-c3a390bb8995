{"version": "5", "dialect": "sqlite", "id": "c742ba5d-5fdf-4e80-ac9b-bee2a6e0426a", "prevId": "5b4ebf25-e38a-43ca-a53b-326b25eaf46c", "tables": {"activity_logs": {"name": "activity_logs", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_type": {"name": "user_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "resource_type": {"name": "resource_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "resource_id": {"name": "resource_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "addresses": {"name": "addresses", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address_1": {"name": "address_1", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address_2": {"name": "address_2", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "country_code": {"name": "country_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "province": {"name": "province", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "postal_code": {"name": "postal_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "admin_users": {"name": "admin_users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'viewer'"}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_login": {"name": "last_login", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"admin_users_email_unique": {"name": "admin_users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "carts": {"name": "carts", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "billing_address_id": {"name": "billing_address_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "shipping_address_id": {"name": "shipping_address_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "region_id": {"name": "region_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_id": {"name": "payment_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'default'"}, "completed_at": {"name": "completed_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_authorized_at": {"name": "payment_authorized_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "idempotency_key": {"name": "idempotency_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "context": {"name": "context", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sales_channel_id": {"name": "sales_channel_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "collection_images": {"name": "collection_images", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "collection_id": {"name": "collection_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "alt_text": {"name": "alt_text", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "countries": {"name": "countries", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "iso_2": {"name": "iso_2", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "iso_3": {"name": "iso_3", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "num_code": {"name": "num_code", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "region_id": {"name": "region_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "customer_group_customers": {"name": "customer_group_customers", "columns": {"customer_group_id": {"name": "customer_group_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "customer_groups": {"name": "customer_groups", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "customers": {"name": "customers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "billing_address_id": {"name": "billing_address_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "has_account": {"name": "has_account", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"customers_email_unique": {"name": "customers_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "email_templates": {"name": "email_templates", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "html": {"name": "html", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "variables": {"name": "variables", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}}, "indexes": {"email_templates_name_unique": {"name": "email_templates_name_unique", "columns": ["name"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "file_uploads": {"name": "file_uploads", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "original_name": {"name": "original_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "mimetype": {"name": "mimetype", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "bucket": {"name": "bucket", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "uploaded_by": {"name": "uploaded_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "fulfillments": {"name": "fulfillments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "claim_order_id": {"name": "claim_order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "swap_id": {"name": "swap_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tracking_numbers": {"name": "tracking_numbers", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "shipped_at": {"name": "shipped_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "canceled_at": {"name": "canceled_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "location_id": {"name": "location_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "no_notification": {"name": "no_notification", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "journal_entries": {"name": "journal_entries", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "author": {"name": "author", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "published": {"name": "published", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "published_at": {"name": "published_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"journal_entries_slug_unique": {"name": "journal_entries_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "notification_events": {"name": "notification_events", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "recipient": {"name": "recipient", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "template_id": {"name": "template_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "sent_at": {"name": "sent_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "order_items": {"name": "order_items", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "variant_id": {"name": "variant_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_title": {"name": "product_title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "variant_title": {"name": "variant_title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "unit_price": {"name": "unit_price", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_price": {"name": "total_price", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "fulfilled_quantity": {"name": "fulfilled_quantity", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "refunded_quantity": {"name": "refunded_quantity", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "orders": {"name": "orders", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "fulfillment_status": {"name": "fulfillment_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'not_fulfilled'"}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'not_paid'"}, "display_id": {"name": "display_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "cart_id": {"name": "cart_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "billing_address_id": {"name": "billing_address_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "shipping_address_id": {"name": "shipping_address_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "region_id": {"name": "region_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency_code": {"name": "currency_code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tax_rate": {"name": "tax_rate", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "draft_order_id": {"name": "draft_order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "no_notification": {"name": "no_notification", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "idempotency_key": {"name": "idempotency_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sales_channel_id": {"name": "sales_channel_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "payment_sessions": {"name": "payment_sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "cart_id": {"name": "cart_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_selected": {"name": "is_selected", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_initiated": {"name": "is_initiated", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_authorized_at": {"name": "payment_authorized_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "payments": {"name": "payments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "swap_id": {"name": "swap_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "cart_id": {"name": "cart_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency_code": {"name": "currency_code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount_refunded": {"name": "amount_refunded", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "captured_at": {"name": "captured_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "canceled_at": {"name": "canceled_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "product_collections": {"name": "product_collections", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "handle": {"name": "handle", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"product_collections_handle_unique": {"name": "product_collections_handle_unique", "columns": ["handle"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "product_images": {"name": "product_images", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "alt_text": {"name": "alt_text", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "product_product_tags": {"name": "product_product_tags", "columns": {"product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tag_id": {"name": "tag_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "product_sales": {"name": "product_sales", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_sales": {"name": "total_sales", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "quantity_sold": {"name": "quantity_sold", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "revenue": {"name": "revenue", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "last_sale_at": {"name": "last_sale_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "product_tags": {"name": "product_tags", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "product_translations": {"name": "product_translations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "language_code": {"name": "language_code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "product_variants": {"name": "product_variants", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "barcode": {"name": "barcode", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ean": {"name": "ean", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "upc": {"name": "upc", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "variant_rank": {"name": "variant_rank", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "inventory_quantity": {"name": "inventory_quantity", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "allow_backorder": {"name": "allow_backorder", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "manage_inventory": {"name": "manage_inventory", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "hs_code": {"name": "hs_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "origin_country": {"name": "origin_country", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "mid_code": {"name": "mid_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "material": {"name": "material", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "weight": {"name": "weight", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "length": {"name": "length", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "height": {"name": "height", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "width": {"name": "width", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "products": {"name": "products", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "subtitle": {"name": "subtitle", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "handle": {"name": "handle", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_giftcard": {"name": "is_giftcard", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'draft'"}, "thumbnail": {"name": "thumbnail", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "weight": {"name": "weight", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "length": {"name": "length", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "height": {"name": "height", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "width": {"name": "width", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "hs_code": {"name": "hs_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "origin_country": {"name": "origin_country", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "mid_code": {"name": "mid_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "material": {"name": "material", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "collection_id": {"name": "collection_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "type_id": {"name": "type_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "discountable": {"name": "discountable", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"products_handle_unique": {"name": "products_handle_unique", "columns": ["handle"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "refunds": {"name": "refunds", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_id": {"name": "payment_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "idempotency_key": {"name": "idempotency_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "regions": {"name": "regions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency_code": {"name": "currency_code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tax_rate": {"name": "tax_rate", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "tax_code": {"name": "tax_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "gift_cards_taxable": {"name": "gift_cards_taxable", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "automatic_taxes": {"name": "automatic_taxes", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "returns": {"name": "returns", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'requested'"}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "swap_id": {"name": "swap_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "claim_order_id": {"name": "claim_order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "shipping_data": {"name": "shipping_data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refund_amount": {"name": "refund_amount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "received_at": {"name": "received_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_type": {"name": "user_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "settings": {"name": "settings", "columns": {"key": {"name": "key", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "shipping_methods": {"name": "shipping_methods", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "shipping_option_id": {"name": "shipping_option_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "cart_id": {"name": "cart_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "swap_id": {"name": "swap_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "return_id": {"name": "return_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "claim_order_id": {"name": "claim_order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "includes_tax": {"name": "includes_tax", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "subtotal": {"name": "subtotal", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "total": {"name": "total", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "tax_total": {"name": "tax_total", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "shipping_options": {"name": "shipping_options", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "region_id": {"name": "region_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "profile_id": {"name": "profile_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price_type": {"name": "price_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_return": {"name": "is_return", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "admin_only": {"name": "admin_only", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "trusted_shop_reviews": {"name": "trusted_shop_reviews", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "customer_name": {"name": "customer_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "verified": {"name": "verified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}