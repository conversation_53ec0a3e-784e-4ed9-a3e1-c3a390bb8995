import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  PlusIcon, 
  PlayIcon,
  CheckIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  LinkIcon 
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { settingsApi } from '../../lib/api';

interface Webhook {
  id: string;
  name: string;
  url: string;
  events: string[];
  is_active: boolean;
  last_success_at?: string;
  last_error?: string;
  created_at: string;
}

const WebhookManager: React.FC = () => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState<Partial<Webhook>>({
    events: [],
    is_active: true
  });
  
  const queryClient = useQueryClient();

  // Available webhook events
  const availableEvents = [
    { value: 'order.created', label: 'Order Created' },
    { value: 'order.updated', label: 'Order Updated' },
    { value: 'order.cancelled', label: 'Order Cancelled' },
    { value: 'payment.completed', label: 'Payment Completed' },
    { value: 'payment.failed', label: 'Payment Failed' },
    { value: 'product.created', label: 'Product Created' },
    { value: 'product.updated', label: 'Product Updated' },
    { value: 'customer.created', label: 'Customer Created' },
    { value: 'inventory.updated', label: 'Inventory Updated' }
  ];

  // Fetch webhooks
  const { data: webhooksData, isLoading } = useQuery({
    queryKey: ['webhooks'],
    queryFn: () => settingsApi.getWebhooks().then(res => res.data)
  });

  // Create webhook mutation
  const createWebhookMutation = useMutation({
    mutationFn: (data: Partial<Webhook>) => settingsApi.createWebhook(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['webhooks'] });
      toast.success('Webhook created successfully');
      setShowAddForm(false);
      setFormData({ events: [], is_active: true });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create webhook');
    }
  });

  // Test webhook mutation
  const testWebhookMutation = useMutation({
    mutationFn: (id: string) => settingsApi.testWebhook(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['webhooks'] });
      toast.success('Webhook test successful');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Webhook test failed');
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.url || !formData.events?.length) {
      toast.error('Please fill in all required fields');
      return;
    }
    createWebhookMutation.mutate(formData);
  };

  const handleEventToggle = (eventValue: string) => {
    const currentEvents = formData.events || [];
    const newEvents = currentEvents.includes(eventValue)
      ? currentEvents.filter(e => e !== eventValue)
      : [...currentEvents, eventValue];
    
    setFormData({ ...formData, events: newEvents });
  };

  const webhooks = webhooksData?.webhooks || [];

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="space-y-3">
          {[1, 2].map(i => (
            <div key={i} className="h-24 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Webhooks</h3>
          <p className="text-sm text-gray-500">
            Configure webhook endpoints to receive real-time notifications
          </p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Webhook
        </button>
      </div>

      {/* Webhook List */}
      <div className="space-y-4">
        {webhooks.length === 0 ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <LinkIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No webhooks configured</h3>
            <p className="text-gray-500 mb-4">
              Set up webhooks to receive real-time notifications about events in your store.
            </p>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Create your first webhook
            </button>
          </div>
        ) : (
          webhooks.map((webhook: Webhook) => (
            <div key={webhook.id} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium text-gray-900">{webhook.name}</h4>
                    <span className={`px-2 py-1 rounded text-xs ${
                      webhook.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {webhook.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3 font-mono bg-gray-50 px-2 py-1 rounded">
                    {webhook.url}
                  </p>
                  
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-700 mb-1">Events:</p>
                    <div className="flex flex-wrap gap-1">
                      {webhook.events.map(event => (
                        <span key={event} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                          {event}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Status indicators */}
                  <div className="flex items-center space-x-4 text-sm">
                    {webhook.last_success_at && (
                      <div className="flex items-center text-green-600">
                        <CheckIcon className="h-4 w-4 mr-1" />
                        Last success: {new Date(webhook.last_success_at).toLocaleDateString()}
                      </div>
                    )}
                    {webhook.last_error && (
                      <div className="flex items-center text-red-600">
                        <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                        Error: {webhook.last_error}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => testWebhookMutation.mutate(webhook.id)}
                    disabled={testWebhookMutation.isPending}
                    className="bg-gray-100 text-gray-700 px-3 py-1 rounded hover:bg-gray-200 flex items-center text-sm"
                  >
                    <PlayIcon className="h-4 w-4 mr-1" />
                    Test
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Add Webhook Form */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b">
              <h3 className="text-lg font-medium">Add Webhook</h3>
              <button 
                onClick={() => setShowAddForm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Webhook Name *
                </label>
                <input
                  type="text"
                  value={formData.name || ''}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Order notifications"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Endpoint URL *
                </label>
                <input
                  type="url"
                  value={formData.url || ''}
                  onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                  placeholder="https://your-app.com/webhooks"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Must be a valid HTTPS URL that can receive POST requests
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Events to Subscribe *
                </label>
                <div className="space-y-2 max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-3">
                  {availableEvents.map(event => (
                    <label key={event.value} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={(formData.events || []).includes(event.value)}
                        onChange={() => handleEventToggle(event.value)}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{event.label}</span>
                    </label>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Select the events you want to receive notifications for
                </p>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_active || false}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label className="ml-2 text-sm text-gray-700">
                  Active (start receiving events immediately)
                </label>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createWebhookMutation.isPending}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {createWebhookMutation.isPending ? 'Creating...' : 'Create Webhook'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default WebhookManager; 