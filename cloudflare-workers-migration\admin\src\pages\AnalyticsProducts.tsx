import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { api, endpoints } from '../lib/api';
import { 
  ChartBarIcon,
  CubeIcon,
  EyeIcon,
  ShoppingCartIcon
} from '@heroicons/react/24/outline';

// Types for the analytics dashboard data
interface DashboardData {
  overview?: {
    total_revenue?: number;
    total_orders?: number;
    total_customers?: number;
    avg_order_value?: number;
    growth_rate?: number;
  };
  recent_orders?: any[];
  top_products?: any[];
  total_products?: number;
  active_products?: number;
  low_stock_products?: number;
}

const AnalyticsProducts: React.FC = () => {
  const [dateRange, setDateRange] = useState('30d');

  // Fetch analytics data
  const { data: analyticsData, isLoading, error } = useQuery({
    queryKey: ['analytics', 'dashboard'],
    queryFn: async () => {
      const response = await api.get(endpoints.analytics.dashboard);
      return response.data.data as DashboardData;
    },
  });

  const StatCard = ({ title, value, icon: Icon, color = 'blue' }: {
    title: string;
    value: string | number;
    icon: React.ComponentType<any>;
    color?: string;
  }) => (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className={`h-6 w-6 text-${color}-600`} />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="text-2xl font-semibold text-gray-900">{value}</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-800">
            Error loading product analytics. Please try again later.
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-200 h-24 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Product Analytics
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Track product performance, inventory, and sales insights
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <StatCard
          title="Total Products"
          value={analyticsData?.total_products || 0}
          icon={CubeIcon}
          color="blue"
        />
        <StatCard
          title="Active Products"
          value={analyticsData?.active_products || 0}
          icon={EyeIcon}
          color="green"
        />
        <StatCard
          title="Low Stock Products"
          value={analyticsData?.low_stock_products || 0}
          icon={ShoppingCartIcon}
          color="red"
        />
        <StatCard
          title="Categories"
          value="0" // Mock data
          icon={ChartBarIcon}
          color="purple"
        />
      </div>

      {/* Top Products */}
      <div className="bg-white shadow rounded-lg mb-8">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Top Selling Products
          </h3>
          {analyticsData?.top_products?.length ? (
            <div className="space-y-4">
              {analyticsData.top_products.map((product, index) => (
                <div key={product.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-indigo-600">#{index + 1}</span>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{product.title}</p>
                      <p className="text-sm text-gray-500">{product.sales} sales</p>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    {new Intl.NumberFormat('ro-RO', {
                      style: 'currency',
                      currency: 'RON',
                    }).format(product.revenue)}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No sales data</h3>
              <p className="mt-1 text-sm text-gray-500">
                Product sales data will appear here once you have orders.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Product Performance */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Product Performance Overview
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {analyticsData?.total_products || 0}
              </div>
              <div className="text-sm text-gray-500">Total Products</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {analyticsData?.active_products || 0}
              </div>
              <div className="text-sm text-gray-500">Active Products</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {analyticsData?.low_stock_products || 0}
              </div>
              <div className="text-sm text-gray-500">Low Stock Products</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsProducts; 