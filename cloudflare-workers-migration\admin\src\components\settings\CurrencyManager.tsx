import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  PlusIcon, 
  XMarkIcon,
  CurrencyDollarIcon 
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { settingsApi } from '../../lib/api';

interface Currency {
  code: string;
  name: string;
  symbol: string;
  symbol_native: string;
  decimal_digits: number;
  rounding: number;
  is_default?: boolean;
}

const CurrencyManager: React.FC = () => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState<Partial<Currency>>({
    decimal_digits: 2,
    rounding: 0
  });
  
  const queryClient = useQueryClient();

  // Fetch currencies
  const { data: currenciesData, isLoading } = useQuery({
    queryKey: ['currencies'],
    queryFn: async () => {
      try {
        const response = await settingsApi.getCurrencies();
        // Ensure we always return a valid object with currencies array
        return {
          currencies: response.data?.currencies || response.data?.data || response.data || []
        };
      } catch (error) {
        console.error('Failed to fetch currencies:', error);
        // Return empty array instead of undefined to prevent React Query errors
        return { currencies: [] };
      }
    }
  });

  // Save currency mutation
  const saveCurrencyMutation = useMutation({
    mutationFn: (data: Partial<Currency>) => settingsApi.saveCurrency(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['currencies'] });
      toast.success('Currency saved successfully');
      setShowAddForm(false);
      setFormData({ decimal_digits: 2, rounding: 0 });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to save currency');
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.code || !formData.name || !formData.symbol) {
      toast.error('Please fill in all required fields');
      return;
    }
    saveCurrencyMutation.mutate(formData);
  };

  const currencies = currenciesData?.currencies || [];

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="space-y-3">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-16 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Currencies</h3>
          <p className="text-sm text-gray-500">
            Manage supported currencies for your store
          </p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Currency
        </button>
      </div>

      {/* Currency List */}
      <div className="space-y-3">
        {currencies.map((currency: Currency) => (
          <div key={currency.code} className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-100 p-2 rounded-lg">
                <CurrencyDollarIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium text-gray-900">{currency.name}</h4>
                  <span className="bg-gray-200 px-2 py-1 rounded text-xs font-mono">
                    {currency.code}
                  </span>
                  {currency.is_default && (
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                      Default
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-500">
                  Symbol: {currency.symbol} • {currency.decimal_digits} decimal places
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => saveCurrencyMutation.mutate({ ...currency, is_default: true })}
                disabled={currency.is_default}
                className="text-blue-600 hover:text-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {currency.is_default ? 'Default' : 'Set Default'}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Add Currency Form */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-md w-full mx-4">
            <div className="flex justify-between items-center p-6 border-b">
              <h3 className="text-lg font-medium">Add Currency</h3>
              <button 
                onClick={() => setShowAddForm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Currency Code *
                </label>
                <input
                  type="text"
                  value={formData.code || ''}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                  placeholder="USD"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  maxLength={3}
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Currency Name *
                </label>
                <input
                  type="text"
                  value={formData.name || ''}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="US Dollar"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Symbol *
                </label>
                <input
                  type="text"
                  value={formData.symbol || ''}
                  onChange={(e) => setFormData({ ...formData, symbol: e.target.value })}
                  placeholder="$"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Native Symbol
                </label>
                <input
                  type="text"
                  value={formData.symbol_native || ''}
                  onChange={(e) => setFormData({ ...formData, symbol_native: e.target.value })}
                  placeholder="$"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Decimal Places
                </label>
                <input
                  type="number"
                  value={formData.decimal_digits || 2}
                  onChange={(e) => setFormData({ ...formData, decimal_digits: parseInt(e.target.value) })}
                  min="0"
                  max="10"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_default || false}
                  onChange={(e) => setFormData({ ...formData, is_default: e.target.checked })}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label className="ml-2 text-sm text-gray-700">
                  Set as default currency
                </label>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saveCurrencyMutation.isPending}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {saveCurrencyMutation.isPending ? 'Saving...' : 'Save Currency'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default CurrencyManager; 