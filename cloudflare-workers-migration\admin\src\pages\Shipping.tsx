import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  TruckIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  MagnifyingGlassIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { api } from '../lib/api';

const Shipping: React.FC = () => {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [showShipModal, setShowShipModal] = useState(false);
  const [trackingNumber, setTrackingNumber] = useState('');
  const [detectedCourier, setDetectedCourier] = useState<any>(null);

  // Fetch shippable orders
  const { data: ordersData, isLoading } = useQuery({
    queryKey: ['shipping', 'orders'],
    queryFn: async () => {
      const response = await api.get('/admin/shipping/orders');
      return response.data.data;
    },
  });

  // Ship order mutation
  const shipOrderMutation = useMutation({
    mutationFn: async ({ orderId, trackingNumber, courier }: { orderId: string; trackingNumber: string; courier?: string }) => {
      return api.post('/admin/shipping/ship', { orderId, trackingNumber, courier });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['shipping'] });
      toast.success(data.data.message);
      setShowShipModal(false);
      setSelectedOrder(null);
      setTrackingNumber('');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'A apărut o eroare.');
    },
  });

  const orders = ordersData?.orders || [];

  const filteredOrders = orders.filter((order: any) =>
    order.number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDetectCourier = async (trackingNum: string) => {
    if (!trackingNum.trim()) return;

    try {
      const response = await api.post('/admin/shipping/detect-courier', { trackingNumber: trackingNum });
      setDetectedCourier(response.data.data);
    } catch (error) {
      console.error('Error detecting courier:', error);
    }
  };

  const openShipModal = (order: any) => {
    setSelectedOrder(order);
    setShowShipModal(true);
    setTrackingNumber('');
    setDetectedCourier(null);
  };

  const handleShipOrder = () => {
    if (!trackingNumber.trim() || !selectedOrder) {
      toast.error('Te rugăm să introduci numărul de urmărire.');
      return;
    }

    shipOrderMutation.mutate({
      orderId: selectedOrder.id,
      trackingNumber: trackingNumber.trim(),
      courier: detectedCourier?.courier
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ro-RO', {
      style: 'currency',
      currency: 'RON',
    }).format(amount / 100);
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Shipping Management</h1>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Caută comenzi..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Comenzi de expediat ({filteredOrders.length})
          </h3>
        </div>

        {filteredOrders.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Comandă</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Client</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Acțiuni</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredOrders.map((order: any) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        #{order.number || order.id.slice(-6)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{order.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(order.total_amount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <Link
                        to={`/orders/${order.id}`}
                        className="text-gray-600 hover:text-gray-800"
                      >
                        <EyeIcon className="h-4 w-4 inline" />
                      </Link>
                      <button
                        onClick={() => openShipModal(order)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs"
                      >
                        Expediază
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <TruckIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Nicio comandă de expediat</h3>
          </div>
        )}
      </div>

      {/* Ship Order Modal */}
      {showShipModal && selectedOrder && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Expediază Comanda #{selectedOrder.number}
              </h3>
              <button
                onClick={() => setShowShipModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Număr de urmărire (AWB) *
                </label>
                <input
                  type="text"
                  value={trackingNumber}
                  onChange={(e) => {
                    setTrackingNumber(e.target.value);
                    handleDetectCourier(e.target.value);
                  }}
                  placeholder="ex: 4040500017778"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {detectedCourier && (
                <div className="p-3 rounded-md bg-green-50 border border-green-200">
                  <div className="flex items-center space-x-2">
                    <CheckCircleIcon className="h-5 w-5 text-green-500" />
                    <div className="text-sm font-medium">
                      Curier detectat: <strong>{detectedCourier.courierName}</strong>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  onClick={() => setShowShipModal(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Anulează
                </button>
                <button
                  onClick={handleShipOrder}
                  disabled={!trackingNumber.trim()}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  Expediază
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Shipping; 