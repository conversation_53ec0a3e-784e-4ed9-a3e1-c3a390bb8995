// <PERSON>ript to create admin user for HandmadeIn.ro
// This calls the backend API endpoint to properly create an admin user with the correct password hashing

const BACKEND_URL = 'https://bapi.handmadein.ro'; // Change this to your backend URL

const adminUserData = {
  email: '<EMAIL>',
  password: 'Secure@dmin2024!', // New secure password
  first_name: 'Admin',
  last_name: 'User'
};

console.log('='.repeat(60));
console.log('CREATING ADMIN USER FOR HANDMADEIN.RO');
console.log('='.repeat(60));
console.log(`Email: ${adminUserData.email}`);
console.log(`Password: ${adminUserData.password}`);
console.log(`Backend URL: ${BACKEND_URL}`);
console.log('');

async function createAdminUser() {
  try {
    console.log('Sending request to create admin user...');
    
    const response = await fetch(`${BACKEND_URL}/auth/admin/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(adminUserData)
    });

    const result = await response.json();

    console.log('Response status:', response.status);
    console.log('Response:', result);

    if (response.ok && result.success) {
      console.log('');
      console.log('✅ SUCCESS: Admin user created successfully!');
      console.log('');
      console.log('Login credentials:');
      console.log(`Email: ${adminUserData.email}`);
      console.log(`Password: ${adminUserData.password}`);
      console.log('');
      console.log('You can now log in to the admin interface.');
    } else {
      console.log('');
      console.log('❌ ERROR: Failed to create admin user');
      console.log('Error:', result.error || 'Unknown error');
      
      if (result.error === 'Admin signup is disabled - admin users already exist') {
        console.log('');
        console.log('Admin users already exist. If you need to update the password, use:');
        console.log('node update-admin-password.js');
        console.log('');
        console.log('Or access the admin interface at /signup if no admin exists yet.');
      }
    }

  } catch (error) {
    console.error('');
    console.error('❌ REQUEST FAILED:', error.message);
    console.error('');
    console.error('Make sure:');
    console.error('1. The backend is running');
    console.error('2. The backend URL is correct');
    console.error('3. You have internet connectivity');
  }
}

// Note: For password updates, use the dedicated update-admin-password.js script
// This function is kept for compatibility but requires the adminKey
async function updateAdminPassword() {
  console.log('');
  console.log('For password updates, please use:');
  console.log('node update-admin-password.js');
  console.log('');
  console.log('Or visit the admin interface at /signup (if no admin exists)');
}

// Main execution
createAdminUser().then(() => {
  console.log('');
  console.log('='.repeat(60));
}).catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});

// Export for use in other scripts
module.exports = {
  createAdminUser,
  updateAdminPassword,
  adminUserData,
  BACKEND_URL
}; 