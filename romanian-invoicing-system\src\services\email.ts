import type { Env } from '../index';

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export interface EmailData {
  to: string;
  cc?: string;
  bcc?: string;
  subject: string;
  html: string;
  text: string;
  attachments?: Array<{
    filename: string;
    content: ArrayBuffer;
    contentType: string;
  }>;
}

export class EmailService {
  private env: Env;

  constructor(env: Env) {
    this.env = env;
  }

  // Send invoice email with PDF attachment
  async sendInvoiceEmail(
    invoice: any,
    client: any,
    company: any,
    pdfBuffer?: ArrayBuffer
  ): Promise<boolean> {
    try {
      const template = this.generateInvoiceEmailTemplate(invoice, client, company);
      
      const emailData: EmailData = {
        to: client.email,
        subject: template.subject,
        html: template.html,
        text: template.text,
        attachments: pdfBuffer ? [{
          filename: `Factura_${invoice.series}${invoice.number}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf'
        }] : undefined
      };

      return await this.sendEmail(emailData);
    } catch (error) {
      console.error('Error sending invoice email:', error);
      return false;
    }
  }

  // Send payment reminder email
  async sendPaymentReminder(
    invoice: any,
    client: any,
    company: any,
    reminderType: 'gentle' | 'firm' | 'final'
  ): Promise<boolean> {
    try {
      const template = this.generateReminderEmailTemplate(invoice, client, company, reminderType);
      
      const emailData: EmailData = {
        to: client.email,
        subject: template.subject,
        html: template.html,
        text: template.text
      };

      return await this.sendEmail(emailData);
    } catch (error) {
      console.error('Error sending payment reminder:', error);
      return false;
    }
  }

  // Send welcome email for new clients
  async sendWelcomeEmail(client: any, company: any): Promise<boolean> {
    try {
      const template = this.generateWelcomeEmailTemplate(client, company);
      
      const emailData: EmailData = {
        to: client.email,
        subject: template.subject,
        html: template.html,
        text: template.text
      };

      return await this.sendEmail(emailData);
    } catch (error) {
      console.error('Error sending welcome email:', error);
      return false;
    }
  }

  // Core email sending function
  private async sendEmail(emailData: EmailData): Promise<boolean> {
    try {
      // Using Cloudflare Email Workers
      const response = await this.env.EMAIL.send({
        from: `${this.env.COMPANY_NAME} <noreply@${this.getDomainFromEmail()}>`,
        to: emailData.to,
        cc: emailData.cc,
        bcc: emailData.bcc,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
        attachments: emailData.attachments
      });

      return response.success;
    } catch (error) {
      console.error('Email sending failed:', error);
      return false;
    }
  }

  // Generate invoice email template
  private generateInvoiceEmailTemplate(invoice: any, client: any, company: any): EmailTemplate {
    const subject = `Factură ${invoice.series}${invoice.number} - ${company.name}`;
    
    const html = `
      <!DOCTYPE html>
      <html lang="ro">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .invoice-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          .button { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Factură Nouă</h1>
            <p>De la ${company.name}</p>
          </div>
          
          <div class="content">
            <p>Bună ziua ${client.name},</p>
            
            <p>Vă transmitem în atașament factura cu următoarele detalii:</p>
            
            <div class="invoice-details">
              <h3>Detalii Factură</h3>
              <p><strong>Numărul facturii:</strong> ${invoice.series}${invoice.number}</p>
              <p><strong>Data emiterii:</strong> ${new Date(invoice.issue_date).toLocaleDateString('ro-RO')}</p>
              ${invoice.due_date ? `<p><strong>Data scadentă:</strong> ${new Date(invoice.due_date).toLocaleDateString('ro-RO')}</p>` : ''}
              <p><strong>Suma totală:</strong> ${parseFloat(invoice.total).toFixed(2)} ${invoice.currency}</p>
              ${invoice.notes ? `<p><strong>Observații:</strong> ${invoice.notes}</p>` : ''}
            </div>
            
            <p>Vă rugăm să efectuați plata în termenul convenit.</p>
            
            ${company.bank_account ? `
              <div class="invoice-details">
                <h3>Detalii Plată</h3>
                <p><strong>Cont bancar:</strong> ${company.bank_account}</p>
                ${company.bank_name ? `<p><strong>Banca:</strong> ${company.bank_name}</p>` : ''}
              </div>
            ` : ''}
            
            <p>Pentru orice întrebări, nu ezitați să ne contactați.</p>
            
            <p>Cu stimă,<br>
            Echipa ${company.name}</p>
          </div>
          
          <div class="footer">
            <p>${company.name}<br>
            ${company.address}, ${company.city}<br>
            CUI: ${company.cui}<br>
            ${company.phone ? `Tel: ${company.phone}<br>` : ''}
            ${company.email ? `Email: ${company.email}` : ''}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Bună ziua ${client.name},

Vă transmitem factura cu următoarele detalii:

Numărul facturii: ${invoice.series}${invoice.number}
Data emiterii: ${new Date(invoice.issue_date).toLocaleDateString('ro-RO')}
${invoice.due_date ? `Data scadentă: ${new Date(invoice.due_date).toLocaleDateString('ro-RO')}` : ''}
Suma totală: ${parseFloat(invoice.total).toFixed(2)} ${invoice.currency}

Vă rugăm să efectuați plata în termenul convenit.

${company.bank_account ? `
Detalii plată:
Cont bancar: ${company.bank_account}
${company.bank_name ? `Banca: ${company.bank_name}` : ''}
` : ''}

Pentru orice întrebări, nu ezitați să ne contactați.

Cu stimă,
Echipa ${company.name}

${company.name}
${company.address}, ${company.city}
CUI: ${company.cui}
${company.phone ? `Tel: ${company.phone}` : ''}
${company.email ? `Email: ${company.email}` : ''}
    `;

    return { subject, html, text };
  }

  // Generate payment reminder template
  private generateReminderEmailTemplate(
    invoice: any, 
    client: any, 
    company: any, 
    reminderType: 'gentle' | 'firm' | 'final'
  ): EmailTemplate {
    const reminderTexts = {
      gentle: {
        subject: `Reamintire prietenoasă - Factură ${invoice.series}${invoice.number}`,
        greeting: 'Reamintire prietenoasă',
        message: 'Vă reamintim în mod prietenos că factura de mai jos nu a fost încă achitată.'
      },
      firm: {
        subject: `Reamintire de plată - Factură ${invoice.series}${invoice.number}`,
        greeting: 'Reamintire de plată',
        message: 'Observăm că factura de mai jos nu a fost încă achitată și a depășit termenul de scadență.'
      },
      final: {
        subject: `URGENT - Ultimă reamintire - Factură ${invoice.series}${invoice.number}`,
        greeting: 'Ultimă reamintire',
        message: 'Aceasta este ultima reamintire pentru factura de mai jos care nu a fost achitată.'
      }
    };

    const reminder = reminderTexts[reminderType];
    const daysOverdue = invoice.due_date ? 
      Math.floor((new Date().getTime() - new Date(invoice.due_date).getTime()) / (1000 * 60 * 60 * 24)) : 0;

    const html = `
      <!DOCTYPE html>
      <html lang="ro">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${reminder.subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: ${reminderType === 'final' ? '#dc2626' : '#f59e0b'}; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .invoice-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .overdue { color: #dc2626; font-weight: bold; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${reminder.greeting}</h1>
            <p>De la ${company.name}</p>
          </div>
          
          <div class="content">
            <p>Bună ziua ${client.name},</p>
            
            <p>${reminder.message}</p>
            
            <div class="invoice-details">
              <h3>Detalii Factură</h3>
              <p><strong>Numărul facturii:</strong> ${invoice.series}${invoice.number}</p>
              <p><strong>Data emiterii:</strong> ${new Date(invoice.issue_date).toLocaleDateString('ro-RO')}</p>
              <p><strong>Data scadentă:</strong> <span class="overdue">${new Date(invoice.due_date).toLocaleDateString('ro-RO')}</span></p>
              ${daysOverdue > 0 ? `<p><strong>Întârziere:</strong> <span class="overdue">${daysOverdue} zile</span></p>` : ''}
              <p><strong>Suma de plată:</strong> <span class="overdue">${parseFloat(invoice.total).toFixed(2)} ${invoice.currency}</span></p>
            </div>
            
            <p>Vă rugăm să efectuați plata cât mai curând posibil pentru a evita eventualele penalități.</p>
            
            ${company.bank_account ? `
              <div class="invoice-details">
                <h3>Detalii Plată</h3>
                <p><strong>Cont bancar:</strong> ${company.bank_account}</p>
                ${company.bank_name ? `<p><strong>Banca:</strong> ${company.bank_name}</p>` : ''}
              </div>
            ` : ''}
            
            <p>Pentru orice întrebări sau clarificări, vă rugăm să ne contactați.</p>
            
            <p>Cu stimă,<br>
            Echipa ${company.name}</p>
          </div>
          
          <div class="footer">
            <p>${company.name}<br>
            ${company.address}, ${company.city}<br>
            CUI: ${company.cui}<br>
            ${company.phone ? `Tel: ${company.phone}<br>` : ''}
            ${company.email ? `Email: ${company.email}` : ''}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Bună ziua ${client.name},

${reminder.message}

Detalii factură:
Numărul facturii: ${invoice.series}${invoice.number}
Data emiterii: ${new Date(invoice.issue_date).toLocaleDateString('ro-RO')}
Data scadentă: ${new Date(invoice.due_date).toLocaleDateString('ro-RO')}
${daysOverdue > 0 ? `Întârziere: ${daysOverdue} zile` : ''}
Suma de plată: ${parseFloat(invoice.total).toFixed(2)} ${invoice.currency}

Vă rugăm să efectuați plata cât mai curând posibil.

${company.bank_account ? `
Detalii plată:
Cont bancar: ${company.bank_account}
${company.bank_name ? `Banca: ${company.bank_name}` : ''}
` : ''}

Cu stimă,
Echipa ${company.name}
    `;

    return { subject: reminder.subject, html, text };
  }

  // Generate welcome email template
  private generateWelcomeEmailTemplate(client: any, company: any): EmailTemplate {
    const subject = `Bun venit în familia ${company.name}!`;
    
    const html = `
      <!DOCTYPE html>
      <html lang="ro">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #10b981; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Bun venit!</h1>
            <p>Mulțumim că ați ales ${company.name}</p>
          </div>
          
          <div class="content">
            <p>Bună ziua ${client.name},</p>
            
            <p>Vă mulțumim că ați ales să colaborați cu ${company.name}!</p>
            
            <p>Suntem încântați să vă avem ca client și ne angajăm să vă oferim servicii de cea mai înaltă calitate.</p>
            
            <p>Pentru orice întrebări sau asistență, nu ezitați să ne contactați:</p>
            
            <ul>
              ${company.phone ? `<li>Telefon: ${company.phone}</li>` : ''}
              ${company.email ? `<li>Email: ${company.email}</li>` : ''}
            </ul>
            
            <p>Așteptăm cu nerăbdare să colaborăm cu dumneavoastră!</p>
            
            <p>Cu stimă,<br>
            Echipa ${company.name}</p>
          </div>
          
          <div class="footer">
            <p>${company.name}<br>
            ${company.address}, ${company.city}<br>
            CUI: ${company.cui}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Bună ziua ${client.name},

Vă mulțumim că ați ales să colaborați cu ${company.name}!

Suntem încântați să vă avem ca client și ne angajăm să vă oferim servicii de cea mai înaltă calitate.

Pentru orice întrebări sau asistență, nu ezitați să ne contactați:
${company.phone ? `Telefon: ${company.phone}` : ''}
${company.email ? `Email: ${company.email}` : ''}

Așteptăm cu nerăbdare să colaborăm cu dumneavoastră!

Cu stimă,
Echipa ${company.name}

${company.name}
${company.address}, ${company.city}
CUI: ${company.cui}
    `;

    return { subject, html, text };
  }

  // Helper function to extract domain from email
  private getDomainFromEmail(): string {
    // Extract domain from company email or use a default
    const defaultDomain = 'invoicing.ro';
    return defaultDomain;
  }
}
