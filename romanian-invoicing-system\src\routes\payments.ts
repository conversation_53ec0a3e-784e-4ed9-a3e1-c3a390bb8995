import { Hono } from 'hono';
import { z } from 'zod';
import type { Env } from '../index';
import { authMiddleware } from '../middleware/auth';
import { PaymentService } from '../services/payments';

const paymentRoutes = new Hono<{ Bindings: Env; Variables: { user: any } }>();

// Apply auth middleware to protected routes
paymentRoutes.use('/api/*', authMiddleware);

// Get payment providers configuration
paymentRoutes.get('/api/providers', async (c) => {
  try {
    const user = c.get('user');
    const companyId = user.company_id;
    
    const paymentService = new PaymentService(c.env);
    const providers = await paymentService.getPaymentProviders(companyId);
    
    return c.json({
      success: true,
      data: providers
    });
  } catch (error) {
    console.error('Get payment providers error:', error);
    return c.json({ success: false, message: 'Eroare la încărcarea furnizorilor de plată' }, 500);
  }
});

// Create payment intent for invoice
paymentRoutes.post('/api/intent', async (c) => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    
    const schema = z.object({
      invoice_id: z.number(),
      provider: z.string().default('stripe'),
      return_url: z.string().optional()
    });
    
    const validation = schema.safeParse(body);
    if (!validation.success) {
      return c.json({ success: false, message: 'Date invalide', errors: validation.error.errors }, 400);
    }
    
    const { invoice_id, provider } = validation.data;
    
    // Get invoice details
    const invoice = await c.env.DB.prepare(`
      SELECT * FROM invoices 
      WHERE id = ? AND company_id = ? AND paid = 0
    `).bind(invoice_id, user.company_id).first();
    
    if (!invoice) {
      return c.json({ success: false, message: 'Factura nu a fost găsită sau este deja plătită' }, 404);
    }
    
    const paymentService = new PaymentService(c.env);
    const paymentIntent = await paymentService.createPaymentIntent(
      invoice_id,
      parseFloat(invoice.total),
      invoice.currency,
      provider
    );
    
    if (!paymentIntent) {
      return c.json({ success: false, message: 'Eroare la crearea intenției de plată' }, 500);
    }
    
    return c.json({
      success: true,
      data: paymentIntent
    });
  } catch (error) {
    console.error('Create payment intent error:', error);
    return c.json({ success: false, message: 'Eroare la crearea intenției de plată' }, 500);
  }
});

// Generate payment link for invoice
paymentRoutes.post('/api/link', async (c) => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    
    const schema = z.object({
      invoice_id: z.number()
    });
    
    const validation = schema.safeParse(body);
    if (!validation.success) {
      return c.json({ success: false, message: 'Date invalide', errors: validation.error.errors }, 400);
    }
    
    const { invoice_id } = validation.data;
    
    // Verify invoice belongs to company
    const invoice = await c.env.DB.prepare(`
      SELECT id FROM invoices 
      WHERE id = ? AND company_id = ? AND paid = 0
    `).bind(invoice_id, user.company_id).first();
    
    if (!invoice) {
      return c.json({ success: false, message: 'Factura nu a fost găsită sau este deja plătită' }, 404);
    }
    
    const paymentService = new PaymentService(c.env);
    const paymentLink = await paymentService.generatePaymentLink(invoice_id);
    
    if (!paymentLink) {
      return c.json({ success: false, message: 'Eroare la generarea link-ului de plată' }, 500);
    }
    
    return c.json({
      success: true,
      data: { payment_link: paymentLink }
    });
  } catch (error) {
    console.error('Generate payment link error:', error);
    return c.json({ success: false, message: 'Eroare la generarea link-ului de plată' }, 500);
  }
});

// Public payment page (no auth required)
paymentRoutes.get('/pay/:token', async (c) => {
  try {
    const token = c.req.param('token');
    
    // Get payment token data
    const tokenData = await c.env.INVOICING_KV.get(`payment_token_${token}`);
    if (!tokenData) {
      return c.html(`
        <html>
          <head><title>Link de plată expirat</title></head>
          <body>
            <h1>Link de plată expirat</h1>
            <p>Acest link de plată a expirat sau nu este valid.</p>
          </body>
        </html>
      `, 404);
    }
    
    const { invoice_id, expires } = JSON.parse(tokenData);
    
    if (Date.now() > expires) {
      return c.html(`
        <html>
          <head><title>Link de plată expirat</title></head>
          <body>
            <h1>Link de plată expirat</h1>
            <p>Acest link de plată a expirat.</p>
          </body>
        </html>
      `, 410);
    }
    
    // Get invoice and company details
    const invoice = await c.env.DB.prepare(`
      SELECT 
        i.*,
        c.name as client_name,
        comp.name as company_name,
        comp.address as company_address,
        comp.city as company_city,
        comp.cui as company_cui
      FROM invoices i
      JOIN clients c ON i.client_id = c.id
      JOIN companies comp ON i.company_id = comp.id
      WHERE i.id = ?
    `).bind(invoice_id).first();
    
    if (!invoice || invoice.paid) {
      return c.html(`
        <html>
          <head><title>Factură deja plătită</title></head>
          <body>
            <h1>Factură deja plătită</h1>
            <p>Această factură a fost deja plătită.</p>
          </body>
        </html>
      `, 410);
    }
    
    // Get payment providers
    const paymentService = new PaymentService(c.env);
    const providers = await paymentService.getPaymentProviders(invoice.company_id);
    const enabledProviders = providers.filter(p => p.enabled);
    
    // Generate payment page HTML
    const paymentPageHTML = this.generatePaymentPageHTML(invoice, enabledProviders, token);
    
    return c.html(paymentPageHTML);
  } catch (error) {
    console.error('Payment page error:', error);
    return c.html(`
      <html>
        <head><title>Eroare</title></head>
        <body>
          <h1>Eroare</h1>
          <p>A apărut o eroare la încărcarea paginii de plată.</p>
        </body>
      </html>
    `, 500);
  }
});

// Webhook endpoints for payment providers
paymentRoutes.post('/webhook/stripe', async (c) => {
  try {
    const signature = c.req.header('stripe-signature');
    const payload = await c.req.json();
    
    const paymentService = new PaymentService(c.env);
    const success = await paymentService.processPaymentWebhook('stripe', payload, signature);
    
    return c.json({ received: success });
  } catch (error) {
    console.error('Stripe webhook error:', error);
    return c.json({ error: 'Webhook processing failed' }, 500);
  }
});

paymentRoutes.post('/webhook/paypal', async (c) => {
  try {
    const payload = await c.req.json();
    
    const paymentService = new PaymentService(c.env);
    const success = await paymentService.processPaymentWebhook('paypal', payload);
    
    return c.json({ received: success });
  } catch (error) {
    console.error('PayPal webhook error:', error);
    return c.json({ error: 'Webhook processing failed' }, 500);
  }
});

// Payment success page
paymentRoutes.get('/success', async (c) => {
  return c.html(`
    <!DOCTYPE html>
    <html lang="ro">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Plată realizată cu succes</title>
      <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .success { color: #10b981; font-size: 24px; margin-bottom: 20px; }
        .message { font-size: 18px; color: #374151; }
      </style>
    </head>
    <body>
      <div class="success">✓ Plată realizată cu succes!</div>
      <div class="message">
        <p>Mulțumim pentru plată. Veți primi o confirmare pe email în scurt timp.</p>
        <p>Puteți închide această fereastră.</p>
      </div>
    </body>
    </html>
  `);
});

// Payment cancel page
paymentRoutes.get('/cancel', async (c) => {
  return c.html(`
    <!DOCTYPE html>
    <html lang="ro">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Plată anulată</title>
      <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .cancel { color: #ef4444; font-size: 24px; margin-bottom: 20px; }
        .message { font-size: 18px; color: #374151; }
      </style>
    </head>
    <body>
      <div class="cancel">✗ Plată anulată</div>
      <div class="message">
        <p>Plata a fost anulată. Puteți încerca din nou oricând.</p>
        <p>Puteți închide această fereastră.</p>
      </div>
    </body>
    </html>
  `);
});

// Helper function to generate payment page HTML
function generatePaymentPageHTML(invoice: any, providers: any[], token: string): string {
  return `
    <!DOCTYPE html>
    <html lang="ro">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Plată Factură ${invoice.series}${invoice.number}</title>
      <style>
        body { font-family: Arial, sans-serif; background: #f3f4f6; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: #2563eb; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
        .content { padding: 30px; }
        .invoice-details { background: #f9fafb; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .amount { font-size: 32px; font-weight: bold; color: #2563eb; text-align: center; margin: 20px 0; }
        .payment-methods { margin: 30px 0; }
        .payment-button { display: block; width: 100%; padding: 15px; margin: 10px 0; background: #2563eb; color: white; text-decoration: none; border-radius: 6px; text-align: center; font-weight: bold; }
        .payment-button:hover { background: #1d4ed8; }
        .footer { text-align: center; padding: 20px; font-size: 12px; color: #6b7280; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Plată Factură</h1>
          <p>De la ${invoice.company_name}</p>
        </div>
        
        <div class="content">
          <div class="invoice-details">
            <h3>Detalii Factură</h3>
            <p><strong>Numărul:</strong> ${invoice.series}${invoice.number}</p>
            <p><strong>Data emiterii:</strong> ${new Date(invoice.issue_date).toLocaleDateString('ro-RO')}</p>
            <p><strong>Client:</strong> ${invoice.client_name}</p>
            ${invoice.due_date ? `<p><strong>Scadența:</strong> ${new Date(invoice.due_date).toLocaleDateString('ro-RO')}</p>` : ''}
          </div>
          
          <div class="amount">${parseFloat(invoice.total).toFixed(2)} ${invoice.currency}</div>
          
          <div class="payment-methods">
            <h3>Alegeți metoda de plată:</h3>
            ${providers.map(provider => `
              <a href="#" class="payment-button" onclick="initiatePayment('${provider.name}')">
                Plătește cu ${provider.name.charAt(0).toUpperCase() + provider.name.slice(1)}
              </a>
            `).join('')}
          </div>
        </div>
        
        <div class="footer">
          <p>${invoice.company_name}<br>
          ${invoice.company_address}, ${invoice.company_city}<br>
          CUI: ${invoice.company_cui}</p>
        </div>
      </div>
      
      <script>
        function initiatePayment(provider) {
          // Redirect to payment processing
          window.location.href = '/payment/process/' + provider + '?token=${token}';
        }
      </script>
    </body>
    </html>
  `;
}

export { paymentRoutes };
