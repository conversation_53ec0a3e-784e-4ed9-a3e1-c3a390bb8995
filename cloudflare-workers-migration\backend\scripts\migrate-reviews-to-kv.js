const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

// This script migrates reviews from the CSV file to Cloudflare KV storage format
// Run with: node migrate-reviews-to-kv.js

const REVIEWS_CSV_PATH = path.join(__dirname, '../reviews.csv');
const OUTPUT_DIR = path.join(__dirname, '../migration-data');

// Create output directory if it doesn't exist
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// KV Storage keys helpers (matching the backend implementation)
const getReviewKey = (reviewId) => `review:${reviewId}`;
const getProductReviewsKey = (productId) => `product_reviews:${productId}`;
const getProductRatingKey = (productId) => `product_rating:${productId}`;
const getCustomerReviewsKey = (customerId) => `customer_reviews:${customerId}`;

// Data structures
const kvOperations = [];
const productReviews = {}; // productId -> array of reviews
const customerReviews = {}; // customerId -> array of reviewIds
const productRatings = {}; // productId -> rating summary

function generateKVOperations() {
  console.log('Starting migration of reviews to KV format...');
  
  return new Promise((resolve, reject) => {
    const reviews = [];
    
    fs.createReadStream(REVIEWS_CSV_PATH)
      .pipe(csv())
      .on('data', (row) => {
        // Transform CSV row to Review object matching backend interface
        const review = {
          id: row.review_id,
          product_id: row.product_id,
          customer_id: row.user_id,
          customer_name: row.user_name,
          customer_email: `${row.user_name.replace(/\s+/g, '').toLowerCase()}@example.com`, // Generate email
          rating: parseInt(row.rating),
          comment: row.comment.replace(/"""/g, '"'), // Clean up CSV escaping
          verified_purchase: row.verified_purchase === 'true',
          order_id: null, // Not available in CSV
          created_at: row.created_at,
          updated_at: row.updated_at,
          status: 'approved', // All existing reviews are approved
          helpful_votes: 0
        };
        
        reviews.push(review);
      })
      .on('end', () => {
        console.log(`Processing ${reviews.length} reviews...`);
        
        // Process each review
        reviews.forEach(review => {
          // 1. Store individual review
          kvOperations.push({
            key: getReviewKey(review.id),
            value: JSON.stringify(review),
            type: 'review'
          });
          
          // 2. Group by product
          if (!productReviews[review.product_id]) {
            productReviews[review.product_id] = [];
          }
          productReviews[review.product_id].push(review);
          
          // 3. Group by customer
          if (review.customer_id) {
            if (!customerReviews[review.customer_id]) {
              customerReviews[review.customer_id] = [];
            }
            customerReviews[review.customer_id].push(review.id);
          }
        });
        
        // Generate product reviews lists
        Object.entries(productReviews).forEach(([productId, reviews]) => {
          kvOperations.push({
            key: getProductReviewsKey(productId),
            value: JSON.stringify(reviews),
            type: 'product_reviews'
          });
        });
        
        // Generate customer reviews lists
        Object.entries(customerReviews).forEach(([customerId, reviewIds]) => {
          kvOperations.push({
            key: getCustomerReviewsKey(customerId),
            value: JSON.stringify(reviewIds),
            type: 'customer_reviews'
          });
        });
        
        // Generate product rating summaries
        Object.entries(productReviews).forEach(([productId, reviews]) => {
          const approvedReviews = reviews.filter(r => r.status === 'approved');
          
          if (approvedReviews.length > 0) {
            const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
            let totalRating = 0;
            
            approvedReviews.forEach(review => {
              distribution[review.rating]++;
              totalRating += review.rating;
            });
            
            const averageRating = totalRating / approvedReviews.length;
            
            const productRating = {
              product_id: productId,
              average_rating: parseFloat(averageRating.toFixed(1)),
              total_reviews: approvedReviews.length,
              rating_distribution: distribution,
              last_updated: new Date().toISOString()
            };
            
            kvOperations.push({
              key: getProductRatingKey(productId),
              value: JSON.stringify(productRating),
              type: 'product_rating'
            });
          }
        });
        
        console.log(`Generated ${kvOperations.length} KV operations:`);
        console.log(`- ${kvOperations.filter(op => op.type === 'review').length} individual reviews`);
        console.log(`- ${kvOperations.filter(op => op.type === 'product_reviews').length} product review lists`);
        console.log(`- ${kvOperations.filter(op => op.type === 'customer_reviews').length} customer review lists`);
        console.log(`- ${kvOperations.filter(op => op.type === 'product_rating').length} product rating summaries`);
        
        resolve();
      })
      .on('error', reject);
  });
}

async function main() {
  try {
    await generateKVOperations();
    
    // Write KV operations to files for manual upload or wrangler usage
    
    // 1. Write as JSON for programmatic usage
    const kvOperationsFile = path.join(OUTPUT_DIR, 'kv-operations.json');
    fs.writeFileSync(kvOperationsFile, JSON.stringify(kvOperations, null, 2));
    console.log(`\nKV operations written to: ${kvOperationsFile}`);
    
    // 2. Write as wrangler commands
    const wranglerCommandsFile = path.join(OUTPUT_DIR, 'wrangler-kv-commands.sh');
    const wranglerCommands = kvOperations.map(op => 
      `wrangler kv:key put "${op.key}" --binding=REVIEWS_KV --preview false "${op.value.replace(/"/g, '\\"')}"`
    ).join('\n');
    
    fs.writeFileSync(wranglerCommandsFile, `#!/bin/bash
# Wrangler commands to populate KV storage with reviews
# Make sure you're in the backend directory and have wrangler configured
# Uses your existing REVIEWS_KV binding from wrangler.toml

echo "Uploading ${kvOperations.length} keys to KV storage using existing REVIEWS_KV binding..."

${wranglerCommands}

echo "Migration completed!"
`);
    console.log(`Wrangler commands written to: ${wranglerCommandsFile}`);
    
    // 3. Write summary statistics
    const summaryFile = path.join(OUTPUT_DIR, 'migration-summary.json');
    const summary = {
      timestamp: new Date().toISOString(),
      total_operations: kvOperations.length,
      reviews_count: kvOperations.filter(op => op.type === 'review').length,
      products_with_reviews: Object.keys(productReviews).length,
      customers_with_reviews: Object.keys(customerReviews).length,
      product_breakdown: Object.entries(productReviews).map(([productId, reviews]) => ({
        product_id: productId,
        review_count: reviews.length,
        average_rating: reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
      }))
    };
    
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
    console.log(`Migration summary written to: ${summaryFile}`);
    
    console.log('\nMigration completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Upload to your existing REVIEWS_KV binding:');
    console.log('   npm run upload:reviews');
    console.log('');
    console.log('2. Alternative options:');
    console.log('   - Individual commands: chmod +x migration-data/wrangler-kv-commands.sh && ./migration-data/wrangler-kv-commands.sh');
    console.log('   - API upload (requires tokens): npm run upload:reviews:api');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

main(); 