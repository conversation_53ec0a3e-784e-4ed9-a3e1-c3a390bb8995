import { Hono } from 'hono';
import { AnalyticsService } from '../../services/analytics';
import { RecommendationService } from '../../services/recommendations';
import { AISearchService } from '../../services/ai-search';

interface WorkerEnv {
  DB: any;
  CACHE: any;
  SESSIONS: any;
  ASSETS: any;
  UPLOADS: any;
  AI: any; // Add AI binding for Cloudflare Workers AI
  JWT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  TYPESENSE_API_KEY: string;
  TRUSTED_SHOP_API_KEY: string;
  ENVIRONMENT: "development" | "staging" | "production";
  FRONTEND_URL: string;
  ADMIN_URL: string;
}

interface AdminUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

type Variables = {
  user: AdminUser;
  userId: string;
};

const app = new Hono<{ Bindings: WorkerEnv; Variables: Variables }>();

/**
 * GET /admin/statistics/dashboard
 * Get comprehensive dashboard analytics
 */
app.get('/dashboard', async (c) => {
  try {
    const analyticsService = new AnalyticsService(c.env);
    const recommendationService = new RecommendationService(c.env);

    // Get dashboard data in parallel
    const [
      dashboardAnalytics,
      popularProducts,
      trendingProducts,
      searchStats
    ] = await Promise.all([
      analyticsService.getDashboardAnalytics(),
      recommendationService.getPopularProducts(10, 'month'),
      recommendationService.getTrendingProducts(10),
      getSearchAnalytics(c.env)
    ]);

    return c.json({
      success: true,
      data: {
        ...dashboardAnalytics,
        popular_products: popularProducts,
        trending_products: trendingProducts,
        search_analytics: searchStats
      }
    });
  } catch (error) {
    console.error('Failed to get dashboard analytics:', error);
    return c.json({
      success: false,
      error: 'Failed to get dashboard analytics'
    }, 500);
  }
});

/**
 * GET /admin/statistics/products/popular
 * Get popular products with filters
 */
app.get('/products/popular', async (c) => {
  try {
    const limit = parseInt(c.req.query('limit') || '20');
    const period = c.req.query('period') as 'week' | 'month' | 'quarter' | 'all' || 'month';
    const collection_id = c.req.query('collection_id');
    const currency_code = c.req.query('currency_code');

    const recommendationService = new RecommendationService(c.env);
    const filters = {
      ...(collection_id && { collection_id }),
      ...(currency_code && { currency_code })
    };

    const popularProducts = await recommendationService.getPopularProducts(
      limit,
      period,
      filters
    );

    return c.json({
      success: true,
      data: popularProducts,
      meta: {
        limit,
        period,
        filters
      }
    });
  } catch (error) {
    console.error('Failed to get popular products:', error);
    return c.json({
      success: false,
      error: 'Failed to get popular products'
    }, 500);
  }
});

/**
 * GET /admin/statistics/products/trending
 * Get trending products
 */
app.get('/products/trending', async (c) => {
  try {
    const limit = parseInt(c.req.query('limit') || '20');
    const collection_id = c.req.query('collection_id');

    const recommendationService = new RecommendationService(c.env);
    const filters = {
      ...(collection_id && { collection_id })
    };

    const trendingProducts = await recommendationService.getTrendingProducts(
      limit,
      filters
    );

    return c.json({
      success: true,
      data: trendingProducts,
      meta: {
        limit,
        filters
      }
    });
  } catch (error) {
    console.error('Failed to get trending products:', error);
    return c.json({
      success: false,
      error: 'Failed to get trending products'
    }, 500);
  }
});

/**
 * GET /admin/statistics/orders/summary
 * Get order statistics summary
 */
app.get('/orders/summary', async (c) => {
  try {
    const period = c.req.query('period') || 'month';
    const summary = await getOrderSummary(c.env, period);

    return c.json({
      success: true,
      data: summary
    });
  } catch (error) {
    console.error('Failed to get order summary:', error);
    return c.json({
      success: false,
      error: 'Failed to get order summary'
    }, 500);
  }
});

/**
 * GET /admin/statistics/revenue/timeline
 * Get revenue timeline data
 */
app.get('/revenue/timeline', async (c) => {
  try {
    const period = c.req.query('period') || 'month';
    const granularity = c.req.query('granularity') || 'day';
    const timeline = await getRevenueTimeline(c.env, period, granularity);

    return c.json({
      success: true,
      data: timeline
    });
  } catch (error) {
    console.error('Failed to get revenue timeline:', error);
    return c.json({
      success: false,
      error: 'Failed to get revenue timeline'
    }, 500);
  }
});

/**
 * GET /admin/statistics/customers/analytics
 * Get customer analytics
 */
app.get('/customers/analytics', async (c) => {
  try {
    const analytics = await getCustomerAnalytics(c.env);

    return c.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Failed to get customer analytics:', error);
    return c.json({
      success: false,
      error: 'Failed to get customer analytics'
    }, 500);
  }
});

/**
 * GET /admin/statistics/search/popular-terms
 * Get popular search terms
 */
app.get('/search/popular-terms', async (c) => {
  try {
    const limit = parseInt(c.req.query('limit') || '20');
    const days = parseInt(c.req.query('days') || '30');

    const analyticsService = new AnalyticsService(c.env);
    const popularTerms = await analyticsService.getPopularSearchTerms(limit, days);

    return c.json({
      success: true,
      data: popularTerms
    });
  } catch (error) {
    console.error('Failed to get popular search terms:', error);
    return c.json({
      success: false,
      error: 'Failed to get popular search terms'
    }, 500);
  }
});

/**
 * POST /admin/statistics/embeddings/reindex
 * Trigger product embeddings reindexing
 */
app.post('/embeddings/reindex', async (c) => {
  try {
    const aiSearchService = new AISearchService(c.env);
    const result = await aiSearchService.reindexAllProducts();

    return c.json({
      success: true,
      data: result,
      message: 'Product embeddings reindexing completed'
    });
  } catch (error) {
    console.error('Failed to reindex embeddings:', error);
    return c.json({
      success: false,
      error: 'Failed to reindex embeddings'
    }, 500);
  }
});

/**
 * GET /admin/statistics/embeddings/status
 * Get embedding indexing status
 */
app.get('/embeddings/status', async (c) => {
  try {
    const aiSearchService = new AISearchService(c.env);
    const stats = await aiSearchService.getSearchStats();

    return c.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Failed to get embedding status:', error);
    return c.json({
      success: false,
      error: 'Failed to get embedding status'
    }, 500);
  }
});

/**
 * POST /admin/statistics/cache/update
 * Update recommendation cache
 */
app.post('/cache/update', async (c) => {
  try {
    const recommendationService = new RecommendationService(c.env);
    await recommendationService.updateRecommendationCache();

    return c.json({
      success: true,
      message: 'Recommendation cache updated successfully'
    });
  } catch (error) {
    console.error('Failed to update cache:', error);
    return c.json({
      success: false,
      error: 'Failed to update cache'
    }, 500);
  }
});

/**
 * GET /admin/statistics/conversion/rates
 * Get conversion rate analytics
 */
app.get('/conversion/rates', async (c) => {
  try {
    const period = c.req.query('period') || 'month';
    const conversionRates = await getConversionRates(c.env, period);

    return c.json({
      success: true,
      data: conversionRates
    });
  } catch (error) {
    console.error('Failed to get conversion rates:', error);
    return c.json({
      success: false,
      error: 'Failed to get conversion rates'
    }, 500);
  }
});

// Helper functions

async function getSearchAnalytics(env: WorkerEnv) {
  try {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const searchStats = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total_searches,
        COUNT(DISTINCT search_term) as unique_terms,
        AVG(results_count) as avg_results_per_search,
        SUM(CASE WHEN results_count = 0 THEN 1 ELSE 0 END) as zero_result_searches
      FROM search_analytics 
      WHERE created_at >= ?
    `).bind(thirtyDaysAgo.toISOString()).first();

    return {
      total_searches: Number(searchStats?.total_searches) || 0,
      unique_terms: Number(searchStats?.unique_terms) || 0,
      avg_results_per_search: Number(searchStats?.avg_results_per_search) || 0,
      zero_result_searches: Number(searchStats?.zero_result_searches) || 0,
      zero_result_rate: searchStats?.total_searches ? 
        (Number(searchStats.zero_result_searches) / Number(searchStats.total_searches) * 100).toFixed(2) : '0'
    };
  } catch (error) {
    console.error('Failed to get search analytics:', error);
    return {
      total_searches: 0,
      unique_terms: 0,
      avg_results_per_search: 0,
      zero_result_searches: 0,
      zero_result_rate: '0'
    };
  }
}

async function getOrderSummary(env: WorkerEnv, period: string) {
  try {
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarter':
        startDate = new Date(now.getFullYear(), now.getMonth() - 3, 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    const orderStats = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total_orders,
        SUM(total_amount) as total_revenue,
        AVG(total_amount) as avg_order_value,
        COUNT(DISTINCT customer_id) as unique_customers,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
        SUM(CASE WHEN status IN ('delivered', 'completed') THEN 1 ELSE 0 END) as completed_orders
      FROM orders 
      WHERE created_at >= ? AND created_at <= ?
    `).bind(startDate.toISOString(), now.toISOString()).first();

    const previousPeriodStart = new Date(startDate.getTime() - (now.getTime() - startDate.getTime()));
    const previousStats = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total_orders,
        SUM(total_amount) as total_revenue,
        AVG(total_amount) as avg_order_value
      FROM orders 
      WHERE created_at >= ? AND created_at < ?
    `).bind(previousPeriodStart.toISOString(), startDate.toISOString()).first();

    const calculateGrowth = (current: number, previous: number) => {
      if (!previous) return 0;
      return ((current - previous) / previous * 100);
    };

    return {
      current_period: {
        total_orders: Number(orderStats?.total_orders) || 0,
        total_revenue: Number(orderStats?.total_revenue) || 0,
        avg_order_value: Number(orderStats?.avg_order_value) || 0,
        unique_customers: Number(orderStats?.unique_customers) || 0,
        completed_orders: Number(orderStats?.completed_orders) || 0,
        cancelled_orders: Number(orderStats?.cancelled_orders) || 0,
        completion_rate: orderStats?.total_orders ? 
          (Number(orderStats.completed_orders) / Number(orderStats.total_orders) * 100).toFixed(2) : '0',
        cancellation_rate: orderStats?.total_orders ? 
          (Number(orderStats.cancelled_orders) / Number(orderStats.total_orders) * 100).toFixed(2) : '0'
      },
      growth: {
        orders: calculateGrowth(
          Number(orderStats?.total_orders) || 0,
          Number(previousStats?.total_orders) || 0
        ).toFixed(2),
        revenue: calculateGrowth(
          Number(orderStats?.total_revenue) || 0,
          Number(previousStats?.total_revenue) || 0
        ).toFixed(2),
        avg_order_value: calculateGrowth(
          Number(orderStats?.avg_order_value) || 0,
          Number(previousStats?.avg_order_value) || 0
        ).toFixed(2)
      }
    };
  } catch (error) {
    console.error('Failed to get order summary:', error);
    throw error;
  }
}

async function getRevenueTimeline(env: WorkerEnv, period: string, granularity: string) {
  try {
    const now = new Date();
    let startDate: Date;
    let dateFormat: string;

    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        dateFormat = '%Y-%m-%d';
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        dateFormat = granularity === 'week' ? '%Y-%W' : '%Y-%m-%d';
        break;
      case 'quarter':
        startDate = new Date(now.getFullYear(), now.getMonth() - 3, 1);
        dateFormat = '%Y-%W';
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        dateFormat = '%Y-%m';
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        dateFormat = '%Y-%m-%d';
    }

    const timelineData = await env.DB.prepare(`
      SELECT 
        strftime(?, created_at) as period,
        COUNT(*) as orders,
        SUM(total_amount) as revenue,
        AVG(total_amount) as avg_order_value,
        COUNT(DISTINCT customer_id) as unique_customers
      FROM orders 
      WHERE created_at >= ? AND created_at <= ?
        AND status NOT IN ('cancelled', 'refunded')
      GROUP BY strftime(?, created_at)
      ORDER BY period ASC
    `).bind(dateFormat, startDate.toISOString(), now.toISOString(), dateFormat).all();

    return timelineData?.results?.map((row: any) => ({
      period: row.period,
      orders: Number(row.orders),
      revenue: Number(row.revenue),
      avg_order_value: Number(row.avg_order_value),
      unique_customers: Number(row.unique_customers)
    })) || [];
  } catch (error) {
    console.error('Failed to get revenue timeline:', error);
    throw error;
  }
}

async function getCustomerAnalytics(env: WorkerEnv) {
  try {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Customer lifetime value analysis
    const clvData = await env.DB.prepare(`
      SELECT 
        AVG(customer_totals.total_spent) as avg_clv,
        AVG(customer_totals.order_count) as avg_orders_per_customer,
        COUNT(CASE WHEN customer_totals.total_spent > 1000 THEN 1 END) as high_value_customers,
        COUNT(*) as total_customers
      FROM (
        SELECT 
          customer_id,
          SUM(total_amount) as total_spent,
          COUNT(*) as order_count
        FROM orders 
        WHERE status NOT IN ('cancelled', 'refunded')
          AND customer_id IS NOT NULL
        GROUP BY customer_id
      ) customer_totals
    `).first();

    // New vs returning customers (last 30 days)
    const customerTypes = await env.DB.prepare(`
      SELECT 
        COUNT(CASE WHEN first_order.is_first THEN 1 END) as new_customers,
        COUNT(CASE WHEN NOT first_order.is_first THEN 1 END) as returning_customers
      FROM (
        SELECT 
          o.customer_id,
          o.created_at,
          CASE 
            WHEN COUNT(*) OVER (PARTITION BY o.customer_id ORDER BY o.created_at ROWS UNBOUNDED PRECEDING) = 1 
            THEN true 
            ELSE false 
          END as is_first
        FROM orders o
        WHERE o.created_at >= ? 
          AND o.customer_id IS NOT NULL
          AND o.status NOT IN ('cancelled', 'refunded')
      ) first_order
    `).bind(thirtyDaysAgo.toISOString()).first();

    // Customer cohort analysis (simplified)
    const cohortData = await env.DB.prepare(`
      SELECT 
        strftime('%Y-%m', created_at) as cohort_month,
        COUNT(DISTINCT customer_id) as customers,
        SUM(total_amount) as revenue
      FROM orders 
      WHERE created_at >= ? 
        AND customer_id IS NOT NULL
        AND status NOT IN ('cancelled', 'refunded')
      GROUP BY strftime('%Y-%m', created_at)
      ORDER BY cohort_month DESC
      LIMIT 12
    `).bind(new Date(now.getFullYear() - 1, now.getMonth(), 1).toISOString()).all();

    return {
      lifetime_value: {
        avg_clv: Number(clvData?.avg_clv) || 0,
        avg_orders_per_customer: Number(clvData?.avg_orders_per_customer) || 0,
        high_value_customers: Number(clvData?.high_value_customers) || 0,
        total_customers: Number(clvData?.total_customers) || 0
      },
      customer_types: {
        new_customers: Number(customerTypes?.new_customers) || 0,
        returning_customers: Number(customerTypes?.returning_customers) || 0
      },
      cohorts: cohortData?.results?.map((row: any) => ({
        month: row.cohort_month,
        customers: Number(row.customers),
        revenue: Number(row.revenue)
      })) || []
    };
  } catch (error) {
    console.error('Failed to get customer analytics:', error);
    throw error;
  }
}

async function getConversionRates(env: WorkerEnv, period: string) {
  try {
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    // Overall conversion rate (product views to purchases)
    const conversionData = await env.DB.prepare(`
      SELECT 
        SUM(pa.views) as total_views,
        SUM(pa.purchases) as total_purchases,
        COUNT(DISTINCT pa.product_id) as products_with_data
      FROM product_analytics pa
      WHERE pa.period_start >= ?
    `).bind(startDate.toISOString().split('T')[0]).first();

    // Product-level conversion rates
    const productConversions = await env.DB.prepare(`
      SELECT 
        p.id,
        pt.title,
        SUM(pa.views) as views,
        SUM(pa.purchases) as purchases,
        CASE 
          WHEN SUM(pa.views) > 0 
          THEN (SUM(pa.purchases) * 100.0 / SUM(pa.views))
          ELSE 0 
        END as conversion_rate
      FROM product_analytics pa
      JOIN products p ON pa.product_id = p.id
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'ro'
      WHERE pa.period_start >= ?
        AND pa.views > 0
      GROUP BY p.id, pt.title
      ORDER BY conversion_rate DESC
      LIMIT 20
    `).bind(startDate.toISOString().split('T')[0]).all();

    const overallConversionRate = conversionData?.total_views ? 
      (Number(conversionData.total_purchases) / Number(conversionData.total_views) * 100) : 0;

    return {
      overall: {
        total_views: Number(conversionData?.total_views) || 0,
        total_purchases: Number(conversionData?.total_purchases) || 0,
        conversion_rate: overallConversionRate.toFixed(2),
        products_tracked: Number(conversionData?.products_with_data) || 0
      },
      by_product: productConversions?.results?.map((row: any) => ({
        product_id: row.id,
        title: row.title,
        views: Number(row.views),
        purchases: Number(row.purchases),
        conversion_rate: Number(row.conversion_rate).toFixed(2)
      })) || []
    };
  } catch (error) {
    console.error('Failed to get conversion rates:', error);
    throw error;
  }
}

export default app; 