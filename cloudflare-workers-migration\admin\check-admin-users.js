const API_BASE_URL = 'https://bapi.handmadein.ro';

async function checkAndCreateAdminUser() {
    try {
        console.log('🔍 Checking existing users in the database...');
        
        // Try to get users using the simplified admin API (this might fail if no auth exists yet)
        try {
            const response = await fetch(`${API_BASE_URL}/debug/env`);
            const debugInfo = await response.json();
            console.log('Debug info:', debugInfo);
        } catch (error) {
            console.log('Could not get debug info:', error.message);
        }

        // For the simplified auth system, we need a user in the 'users' table
        // The password is hardcoded as 'admin123' in the simplified auth
        
        console.log('\n📋 ADMIN USER SETUP FOR SIMPLIFIED AUTH:');
        console.log('===============================================');
        console.log('The simplified admin auth system expects:');
        console.log('1. A user record in the "users" table with your email');
        console.log('2. The user must have is_active = 1');
        console.log('3. The password is hardcoded as "admin123"');
        console.log('4. The user must have role = "admin"');
        console.log('');
        console.log('Since we cannot directly query the database from this script,');
        console.log('you need to manually create a user record.');
        console.log('');
        console.log('Option 1: Use your database admin panel to run this SQL:');
        console.log('```sql');
        console.log(`INSERT INTO users (
    id, 
    email, 
    first_name, 
    last_name, 
    role, 
    is_active, 
    password_hash,
    created_at,
    updated_at
) VALUES (
    'user_' || strftime('%s', 'now') || '_' || substr(hex(randomblob(8)), 1, 8),
    '<EMAIL>',
    'Admin',
    'User',
    'admin',
    1,
    'admin123',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);`);
        console.log('```');
        console.log('');
        console.log('Option 2: The system might auto-create users during signup process.');
        console.log('');
        console.log('🔑 LOGIN CREDENTIALS:');
        console.log('Email: <EMAIL> (or any email in users table)');
        console.log('Password: admin123');
        console.log('');
        console.log('💡 TIP: Check the browser network tab and server logs');
        console.log('for detailed debugging information.');

    } catch (error) {
        console.error('❌ Error:', error);
        
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            console.log('\n🌐 Network Error - Please check:');
            console.log('1. Backend server is running');
            console.log('2. API URL is correct: ' + API_BASE_URL);
            console.log('3. CORS is properly configured');
        }
    }
}

checkAndCreateAdminUser(); 